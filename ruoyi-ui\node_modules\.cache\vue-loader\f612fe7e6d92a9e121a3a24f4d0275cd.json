{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\category\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\category\\index.vue", "mtime": 1754016617850}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0Q2F0ZWdvcnksIGdldENhdGVnb3J5LCBkZWxDYXRlZ29yeSwgYWRkQ2F0ZWdvcnksIHVwZGF0ZUNhdGVnb3J5IH0gZnJvbSAiQC9hcGkvYml6L2NhdGVnb3J5Ig0KaW1wb3J0IFRyZWVzZWxlY3QgZnJvbSAiQHJpb3BoYWUvdnVlLXRyZWVzZWxlY3QiDQppbXBvcnQgIkByaW9waGFlL3Z1ZS10cmVlc2VsZWN0L2Rpc3QvdnVlLXRyZWVzZWxlY3QuY3NzIg0KDQpleHBvcnQgZGVmYXVsdCB7DQogIG5hbWU6ICJDYXRlZ29yeSIsDQogIGNvbXBvbmVudHM6IHsgVHJlZXNlbGVjdCB9LA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICAvLyDpga7nvanlsYINCiAgICAgIGxvYWRpbmc6IHRydWUsDQogICAgICAvLyDpgInkuK3mlbDnu4QNCiAgICAgIGlkczogW10sDQogICAgICAvLyDpnZ7ljZXkuKrnpoHnlKgNCiAgICAgIHNpbmdsZTogdHJ1ZSwNCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqA0KICAgICAgbXVsdGlwbGU6IHRydWUsDQogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YNCiAgICAgIHNob3dTZWFyY2g6IHRydWUsDQogICAgICAvLyDmgLvmnaHmlbANCiAgICAgIHRvdGFsOiAwLA0KICAgICAgLy8g5YiG57G76KGo5qC85pWw5o2uDQogICAgICBjYXRlZ29yeUxpc3Q6IFtdLA0KICAgICAgLy8g5YiG57G75qCR6YCJ6aG5DQogICAgICBjYXRlZ29yeU9wdGlvbnM6IFtdLA0KICAgICAgLy8g5by55Ye65bGC5qCH6aKYDQogICAgICB0aXRsZTogIiIsDQogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYINCiAgICAgIG9wZW46IGZhbHNlLA0KICAgICAgLy8g5piv5ZCm5bGV5byA77yM6buY6K6k5Y+q5bGV5byAMee6pw0KICAgICAgaXNFeHBhbmRBbGw6IGZhbHNlLA0KICAgICAgLy8g6buY6K6k5bGV5byA55qE6IqC54K5a2V5cw0KICAgICAgZGVmYXVsdEV4cGFuZGVkS2V5czogW10sDQogICAgICAvLyDph43mlrDmuLLmn5PooajmoLznirbmgIENCiAgICAgIHJlZnJlc2hUYWJsZTogdHJ1ZSwNCiAgICAgIC8vIOafpeivouWPguaVsA0KICAgICAgcXVlcnlQYXJhbXM6IHsNCiAgICAgICAgcGFnZU51bTogMSwNCiAgICAgICAgcGFnZVNpemU6IDEwMDAsDQogICAgICAgIHBhcmVudElkOiBudWxsLA0KICAgICAgICBuYW1lOiBudWxsLA0KICAgICAgICBvcmRlck51bTogbnVsbCwNCiAgICAgIH0sDQogICAgICAvLyDooajljZXlj4LmlbANCiAgICAgIGZvcm06IHt9LA0KICAgICAgLy8g6KGo5Y2V5qCh6aqMDQogICAgICBydWxlczogew0KICAgICAgICBwYXJlbnRJZDogWw0KICAgICAgICAgIHsgcmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLkuIrnuqfliIbnsbvkuI3og73kuLrnqboiLCB0cmlnZ2VyOiAiYmx1ciIgfQ0KICAgICAgICBdLA0KICAgICAgICBuYW1lOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuWIhuexu+WQjeensOS4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9DQogICAgICAgIF0sDQogICAgICAgIG9yZGVyTnVtOiBbDQogICAgICAgICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuaYvuekuumhuuW6j+S4jeiDveS4uuepuiIsIHRyaWdnZXI6ICJibHVyIiB9DQogICAgICAgIF0sDQogICAgICB9DQogICAgfQ0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuZ2V0TGlzdCgpDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvKiog5p+l6K+i5YiG57G75YiX6KGoICovDQogICAgZ2V0TGlzdCgpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWUNCiAgICAgIGxpc3RDYXRlZ29yeSh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5jYXRlZ29yeUxpc3QgPSB0aGlzLmhhbmRsZVRyZWUocmVzcG9uc2Uucm93cywgImlkIiwgInBhcmVudElkIikNCiAgICAgICAgdGhpcy50b3RhbCA9IHJlc3BvbnNlLnRvdGFsDQoNCg0KICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZQ0KDQogICAgICAgIC8vIOWcqOS4i+S4gOS4qnRpY2vkuK3orr7nva7pu5jorqTlsZXlvIDnrKzkuIDnuqfoioLngrkNCiAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICAgIGlmICghdGhpcy5pc0V4cGFuZEFsbCkgew0KICAgICAgICAgICAgdGhpcy5leHBhbmRGaXJzdExldmVsTm9kZXMoKQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgIH0pDQogICAgfSwNCiAgICAvKiog6K6+572u6buY6K6k5bGV5byA56ys5LiA57qn6IqC54K5ICovDQogICAgc2V0RGVmYXVsdEV4cGFuZGVkS2V5cygpIHsNCiAgICAgIHRoaXMuZGVmYXVsdEV4cGFuZGVkS2V5cyA9IFtdDQogICAgICBpZiAodGhpcy5jYXRlZ29yeUxpc3QgJiYgdGhpcy5jYXRlZ29yeUxpc3QubGVuZ3RoID4gMCkgew0KICAgICAgICAvLyDpgJLlvZLmn6Xmib7lubblsZXlvIDnrKzkuIDnuqfoioLngrkNCiAgICAgICAgdGhpcy5leHBhbmRGaXJzdExldmVsKHRoaXMuY2F0ZWdvcnlMaXN0LCAwKQ0KDQogICAgICB9DQogICAgfSwNCiAgICAvKiog6YCS5b2S5bGV5byA56ys5LiA57qn6IqC54K5ICovDQogICAgZXhwYW5kRmlyc3RMZXZlbChub2RlcywgbGV2ZWwpIHsNCiAgICAgIGlmICghbm9kZXMgfHwgbm9kZXMubGVuZ3RoID09PSAwKSByZXR1cm4NCg0KICAgICAgbm9kZXMuZm9yRWFjaChub2RlID0+IHsNCiAgICAgICAgaWYgKGxldmVsID09PSAwICYmIG5vZGUuY2hpbGRyZW4gJiYgbm9kZS5jaGlsZHJlbi5sZW5ndGggPiAwKSB7DQogICAgICAgICAgLy8g5bGV5byA5qC56IqC54K577yI56ysMOe6p++8ie+8jOi/meagt+WPr+S7peeci+WIsOesrDHnuqfnmoTlrZDoioLngrkNCiAgICAgICAgICB0aGlzLmRlZmF1bHRFeHBhbmRlZEtleXMucHVzaChub2RlLmlkKQ0KICAgICAgICB9DQogICAgICAgIC8vIOmAkuW9kuWkhOeQhuWtkOiKgueCuQ0KICAgICAgICBpZiAobm9kZS5jaGlsZHJlbiAmJiBub2RlLmNoaWxkcmVuLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICB0aGlzLmV4cGFuZEZpcnN0TGV2ZWwobm9kZS5jaGlsZHJlbiwgbGV2ZWwgKyAxKQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgLyoqIOaJi+WKqOWxleW8gOesrOS4gOe6p+iKgueCuSAqLw0KICAgIGV4cGFuZEZpcnN0TGV2ZWxOb2RlcygpIHsNCiAgICAgIGlmICh0aGlzLiRyZWZzLmNhdGVnb3J5VGFibGUgJiYgdGhpcy5jYXRlZ29yeUxpc3QgJiYgdGhpcy5jYXRlZ29yeUxpc3QubGVuZ3RoID4gMCkgew0KICAgICAgICAvLyDpgY3ljobmoLnoioLngrnvvIzmiYvliqjlsZXlvIDmnInlrZDoioLngrnnmoTmoLnoioLngrkNCiAgICAgICAgdGhpcy5jYXRlZ29yeUxpc3QuZm9yRWFjaChyb290Tm9kZSA9PiB7DQogICAgICAgICAgaWYgKHJvb3ROb2RlLmNoaWxkcmVuICYmIHJvb3ROb2RlLmNoaWxkcmVuLmxlbmd0aCA+IDApIHsNCg0KICAgICAgICAgICAgdGhpcy4kcmVmcy5jYXRlZ29yeVRhYmxlLnRvZ2dsZVJvd0V4cGFuc2lvbihyb290Tm9kZSwgdHJ1ZSkNCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICB9DQogICAgfSwNCiAgICAvKiog6L2s5o2i5YiG57G75pWw5o2u57uT5p6EICovDQogICAgbm9ybWFsaXplcihub2RlKSB7DQogICAgICBpZiAobm9kZS5jaGlsZHJlbiAmJiAhbm9kZS5jaGlsZHJlbi5sZW5ndGgpIHsNCiAgICAgICAgZGVsZXRlIG5vZGUuY2hpbGRyZW4NCiAgICAgIH0NCiAgICAgIHJldHVybiB7DQogICAgICAgIGlkOiBub2RlLmlkLA0KICAgICAgICBsYWJlbDogbm9kZS5uYW1lLA0KICAgICAgICBjaGlsZHJlbjogbm9kZS5jaGlsZHJlbg0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5Y+W5raI5oyJ6ZKuDQogICAgY2FuY2VsKCkgew0KICAgICAgdGhpcy5vcGVuID0gZmFsc2UNCiAgICAgIHRoaXMucmVzZXQoKQ0KICAgIH0sDQogICAgLy8g6KGo5Y2V6YeN572uDQogICAgcmVzZXQoKSB7DQogICAgICB0aGlzLmZvcm0gPSB7DQogICAgICAgIGlkOiBudWxsLA0KICAgICAgICBwYXJlbnRJZDogMCwNCiAgICAgICAgbmFtZTogbnVsbCwNCiAgICAgICAgb3JkZXJOdW06IDAsDQogICAgICAgIGNyZWF0ZVRpbWU6IG51bGwsDQogICAgICAgIHVwZGF0ZVRpbWU6IG51bGwNCiAgICAgIH0NCiAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIikNCiAgICB9LA0KICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVRdWVyeSgpIHsNCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFnZU51bSA9IDENCiAgICAgIHRoaXMuZ2V0TGlzdCgpDQogICAgfSwNCiAgICAvKiog6YeN572u5oyJ6ZKu5pON5L2cICovDQogICAgcmVzZXRRdWVyeSgpIHsNCiAgICAgIHRoaXMucmVzZXRGb3JtKCJxdWVyeUZvcm0iKQ0KICAgICAgdGhpcy5oYW5kbGVRdWVyeSgpDQogICAgfSwNCiAgICAvLyDlpJrpgInmoYbpgInkuK3mlbDmja4NCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uKSB7DQogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLmlkKQ0KICAgICAgdGhpcy5zaW5nbGUgPSBzZWxlY3Rpb24ubGVuZ3RoIT09MQ0KICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoDQogICAgfSwNCiAgICAvKiog5paw5aKe5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlQWRkKHJvdykgew0KICAgICAgdGhpcy5yZXNldCgpDQogICAgICBpZiAocm93ICE9IHVuZGVmaW5lZCkgew0KICAgICAgICB0aGlzLmZvcm0ucGFyZW50SWQgPSByb3cuaWQNCiAgICAgIH0NCiAgICAgIHRoaXMub3BlbiA9IHRydWUNCiAgICAgIHRoaXMudGl0bGUgPSAi5re75Yqg5YiG57G7Ig0KICAgICAgbGlzdENhdGVnb3J5KHsgcGFnZVNpemU6IDEwMDAgfSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMuY2F0ZWdvcnlPcHRpb25zID0gdGhpcy5oYW5kbGVUcmVlKHJlc3BvbnNlLnJvd3MsICJpZCIsICJwYXJlbnRJZCIpDQogICAgICB9KQ0KICAgIH0sDQogICAgLyoqIOWxleW8gC/mipjlj6Dmk43kvZwgKi8NCiAgICB0b2dnbGVFeHBhbmRBbGwoKSB7DQogICAgICB0aGlzLnJlZnJlc2hUYWJsZSA9IGZhbHNlDQogICAgICB0aGlzLmlzRXhwYW5kQWxsID0gIXRoaXMuaXNFeHBhbmRBbGwNCg0KICAgICAgaWYgKCF0aGlzLmlzRXhwYW5kQWxsKSB7DQogICAgICAgIC8vIOWmguaenOWIh+aNouWIsOmdnuWFqOmDqOWxleW8gOeKtuaAge+8jOaJi+WKqOWxleW8gOesrOS4gOe6pw0KICAgICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAgICAgdGhpcy5yZWZyZXNoVGFibGUgPSB0cnVlDQogICAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICAgICAgdGhpcy5leHBhbmRGaXJzdExldmVsTm9kZXMoKQ0KICAgICAgICAgIH0pDQogICAgICAgIH0pDQogICAgICB9IGVsc2Ugew0KICAgICAgICAvLyDlpoLmnpzliIfmjaLliLDlhajpg6jlsZXlvIDnirbmgIENCiAgICAgICAgdGhpcy5kZWZhdWx0RXhwYW5kZWRLZXlzID0gW10NCiAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICAgIHRoaXMucmVmcmVzaFRhYmxlID0gdHJ1ZQ0KICAgICAgICB9KQ0KICAgICAgfQ0KICAgIH0sDQogICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVVwZGF0ZShyb3cpIHsNCiAgICAgIHRoaXMucmVzZXQoKQ0KICAgICAgY29uc3QgaWQgPSByb3cuaWQgfHwgdGhpcy5pZHMNCiAgICAgIGdldENhdGVnb3J5KGlkKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5mb3JtID0gcmVzcG9uc2UuZGF0YQ0KICAgICAgICB0aGlzLm9wZW4gPSB0cnVlDQogICAgICAgIHRoaXMudGl0bGUgPSAi5L+u5pS55YiG57G7Ig0KICAgICAgICBsaXN0Q2F0ZWdvcnkoeyBwYWdlU2l6ZTogMTAwMCB9KS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICB0aGlzLmNhdGVnb3J5T3B0aW9ucyA9IHRoaXMuaGFuZGxlVHJlZShyZXNwb25zZS5yb3dzLCAiaWQiLCAicGFyZW50SWQiKQ0KICAgICAgICB9KQ0KICAgICAgfSkNCiAgICB9LA0KICAgIC8qKiDmj5DkuqTmjInpkq4gKi8NCiAgICBzdWJtaXRGb3JtKCkgew0KICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsNCiAgICAgICAgaWYgKHZhbGlkKSB7DQogICAgICAgICAgaWYgKHRoaXMuZm9ybS5pZCAhPSBudWxsKSB7DQogICAgICAgICAgICB1cGRhdGVDYXRlZ29yeSh0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLkv67mlLnmiJDlip8iKQ0KICAgICAgICAgICAgICB0aGlzLm9wZW4gPSBmYWxzZQ0KICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKQ0KICAgICAgICAgICAgfSkNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgYWRkQ2F0ZWdvcnkodGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5paw5aKe5oiQ5YqfIikNCiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2UNCiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCkNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZURlbGV0ZShyb3cpIHsNCiAgICAgIGNvbnN0IGlkcyA9IHJvdy5pZCB8fCB0aGlzLmlkcw0KICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgn5piv5ZCm56Gu6K6k5Yig6Zmk5YiG57G757yW5Y+35Li6IicgKyBpZHMgKyAnIueahOaVsOaNrumhue+8nycpLnRoZW4oZnVuY3Rpb24oKSB7DQogICAgICAgIHJldHVybiBkZWxDYXRlZ29yeShpZHMpDQogICAgICB9KS50aGVuKCgpID0+IHsNCiAgICAgICAgdGhpcy5nZXRMaXN0KCkNCiAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5Yig6Zmk5oiQ5YqfIikNCiAgICAgIH0pLmNhdGNoKCgpID0+IHt9KQ0KICAgIH0sDQogICAgLyoqIOWvvOWHuuaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZUV4cG9ydCgpIHsNCiAgICAgIHRoaXMuZG93bmxvYWQoJ2Jpei9jYXRlZ29yeS9leHBvcnQnLCB7DQogICAgICAgIC4uLnRoaXMucXVlcnlQYXJhbXMNCiAgICAgIH0sIGBjYXRlZ29yeV8ke25ldyBEYXRlKCkuZ2V0VGltZSgpfS54bHN4YCkNCiAgICB9DQogIH0NCn0NCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgJA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/biz/category", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n      <el-form-item label=\"分类名称\" prop=\"name\">\r\n        <el-input\r\n          v-model=\"queryParams.name\"\r\n          placeholder=\"请输入分类名称\"\r\n          clearable\r\n          @keyup.enter.native=\"handleQuery\"\r\n        />\r\n      </el-form-item>\r\n      <el-form-item>\r\n        <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n        <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n      </el-form-item>\r\n    </el-form>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['biz:category:add']\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n          v-hasPermi=\"['biz:category:edit']\"\r\n        >修改</el-button>\r\n      </el-col>\r\n<!--      <el-col :span=\"1.5\">-->\r\n<!--        <el-button-->\r\n<!--          type=\"danger\"-->\r\n<!--          plain-->\r\n<!--          icon=\"el-icon-delete\"-->\r\n<!--          size=\"mini\"-->\r\n<!--          :disabled=\"multiple\"-->\r\n<!--          @click=\"handleDelete\"-->\r\n<!--          v-hasPermi=\"['biz:category:remove']\"-->\r\n<!--        >删除</el-button>-->\r\n<!--      </el-col>-->\r\n<!--      <el-col :span=\"1.5\">-->\r\n<!--        <el-button-->\r\n<!--          type=\"warning\"-->\r\n<!--          plain-->\r\n<!--          icon=\"el-icon-download\"-->\r\n<!--          size=\"mini\"-->\r\n<!--          @click=\"handleExport\"-->\r\n<!--          v-hasPermi=\"['biz:category:export']\"-->\r\n<!--        >导出</el-button>-->\r\n<!--      </el-col>-->\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"info\"\r\n          plain\r\n          icon=\"el-icon-sort\"\r\n          size=\"mini\"\r\n          @click=\"toggleExpandAll\"\r\n        >展开/折叠</el-button>\r\n      </el-col>\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table border\r\n      v-if=\"refreshTable\"\r\n      v-loading=\"loading\"\r\n      :data=\"categoryList\"\r\n      row-key=\"id\"\r\n      ref=\"categoryTable\"\r\n      :default-expand-all=\"isExpandAll\"\r\n      :default-expanded-keys=\"isExpandAll ? [] : defaultExpandedKeys\"\r\n      :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\"\r\n      @selection-change=\"handleSelectionChange\"\r\n    >\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"分类名称\" align=\"left\" prop=\"name\" width=\"260\" />\r\n      <el-table-column label=\"显示顺序\" align=\"center\" prop=\"orderNum\" width=\"200\" />\r\n      <el-table-column label=\"操作\" align=\"center\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['biz:category:edit']\"\r\n          >修改</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-plus\"\r\n            @click=\"handleAdd(scope.row)\"\r\n            v-hasPermi=\"['biz:category:add']\"\r\n          >新增</el-button>\r\n          <el-button\r\n            v-if=\"scope.row.parentId != 0\"\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['biz:category:remove']\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n\r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加或修改分类对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"500px\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-form-item label=\"上级分类\" prop=\"parentId\" v-if=\"form.parentId !== 0\">\r\n          <treeselect v-model=\"form.parentId\" :options=\"categoryOptions\" :normalizer=\"normalizer\" placeholder=\"选择上级分类\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"分类名称\" prop=\"name\">\r\n          <el-input v-model=\"form.name\" placeholder=\"请输入分类名称\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"显示顺序\" prop=\"orderNum\">\r\n          <el-input-number v-model=\"form.orderNum\" controls-position=\"right\" :min=\"0\" />\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listCategory, getCategory, delCategory, addCategory, updateCategory } from \"@/api/biz/category\"\r\nimport Treeselect from \"@riophae/vue-treeselect\"\r\nimport \"@riophae/vue-treeselect/dist/vue-treeselect.css\"\r\n\r\nexport default {\r\n  name: \"Category\",\r\n  components: { Treeselect },\r\n  data() {\r\n    return {\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 分类表格数据\r\n      categoryList: [],\r\n      // 分类树选项\r\n      categoryOptions: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 是否展开，默认只展开1级\r\n      isExpandAll: false,\r\n      // 默认展开的节点keys\r\n      defaultExpandedKeys: [],\r\n      // 重新渲染表格状态\r\n      refreshTable: true,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 1000,\r\n        parentId: null,\r\n        name: null,\r\n        orderNum: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        parentId: [\r\n          { required: true, message: \"上级分类不能为空\", trigger: \"blur\" }\r\n        ],\r\n        name: [\r\n          { required: true, message: \"分类名称不能为空\", trigger: \"blur\" }\r\n        ],\r\n        orderNum: [\r\n          { required: true, message: \"显示顺序不能为空\", trigger: \"blur\" }\r\n        ],\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n  },\r\n  methods: {\r\n    /** 查询分类列表 */\r\n    getList() {\r\n      this.loading = true\r\n      listCategory(this.queryParams).then(response => {\r\n        this.categoryList = this.handleTree(response.rows, \"id\", \"parentId\")\r\n        this.total = response.total\r\n\r\n\r\n        this.loading = false\r\n\r\n        // 在下一个tick中设置默认展开第一级节点\r\n        this.$nextTick(() => {\r\n          if (!this.isExpandAll) {\r\n            this.expandFirstLevelNodes()\r\n          }\r\n        })\r\n      })\r\n    },\r\n    /** 设置默认展开第一级节点 */\r\n    setDefaultExpandedKeys() {\r\n      this.defaultExpandedKeys = []\r\n      if (this.categoryList && this.categoryList.length > 0) {\r\n        // 递归查找并展开第一级节点\r\n        this.expandFirstLevel(this.categoryList, 0)\r\n\r\n      }\r\n    },\r\n    /** 递归展开第一级节点 */\r\n    expandFirstLevel(nodes, level) {\r\n      if (!nodes || nodes.length === 0) return\r\n\r\n      nodes.forEach(node => {\r\n        if (level === 0 && node.children && node.children.length > 0) {\r\n          // 展开根节点（第0级），这样可以看到第1级的子节点\r\n          this.defaultExpandedKeys.push(node.id)\r\n        }\r\n        // 递归处理子节点\r\n        if (node.children && node.children.length > 0) {\r\n          this.expandFirstLevel(node.children, level + 1)\r\n        }\r\n      })\r\n    },\r\n    /** 手动展开第一级节点 */\r\n    expandFirstLevelNodes() {\r\n      if (this.$refs.categoryTable && this.categoryList && this.categoryList.length > 0) {\r\n        // 遍历根节点，手动展开有子节点的根节点\r\n        this.categoryList.forEach(rootNode => {\r\n          if (rootNode.children && rootNode.children.length > 0) {\r\n\r\n            this.$refs.categoryTable.toggleRowExpansion(rootNode, true)\r\n          }\r\n        })\r\n      }\r\n    },\r\n    /** 转换分类数据结构 */\r\n    normalizer(node) {\r\n      if (node.children && !node.children.length) {\r\n        delete node.children\r\n      }\r\n      return {\r\n        id: node.id,\r\n        label: node.name,\r\n        children: node.children\r\n      }\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.reset()\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        id: null,\r\n        parentId: 0,\r\n        name: null,\r\n        orderNum: 0,\r\n        createTime: null,\r\n        updateTime: null\r\n      }\r\n      this.resetForm(\"form\")\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\")\r\n      this.handleQuery()\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.id)\r\n      this.single = selection.length!==1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd(row) {\r\n      this.reset()\r\n      if (row != undefined) {\r\n        this.form.parentId = row.id\r\n      }\r\n      this.open = true\r\n      this.title = \"添加分类\"\r\n      listCategory({ pageSize: 1000 }).then(response => {\r\n        this.categoryOptions = this.handleTree(response.rows, \"id\", \"parentId\")\r\n      })\r\n    },\r\n    /** 展开/折叠操作 */\r\n    toggleExpandAll() {\r\n      this.refreshTable = false\r\n      this.isExpandAll = !this.isExpandAll\r\n\r\n      if (!this.isExpandAll) {\r\n        // 如果切换到非全部展开状态，手动展开第一级\r\n        this.$nextTick(() => {\r\n          this.refreshTable = true\r\n          this.$nextTick(() => {\r\n            this.expandFirstLevelNodes()\r\n          })\r\n        })\r\n      } else {\r\n        // 如果切换到全部展开状态\r\n        this.defaultExpandedKeys = []\r\n        this.$nextTick(() => {\r\n          this.refreshTable = true\r\n        })\r\n      }\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset()\r\n      const id = row.id || this.ids\r\n      getCategory(id).then(response => {\r\n        this.form = response.data\r\n        this.open = true\r\n        this.title = \"修改分类\"\r\n        listCategory({ pageSize: 1000 }).then(response => {\r\n          this.categoryOptions = this.handleTree(response.rows, \"id\", \"parentId\")\r\n        })\r\n      })\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.id != null) {\r\n            updateCategory(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\")\r\n              this.open = false\r\n              this.getList()\r\n            })\r\n          } else {\r\n            addCategory(this.form).then(response => {\r\n              this.$modal.msgSuccess(\"新增成功\")\r\n              this.open = false\r\n              this.getList()\r\n            })\r\n          }\r\n        }\r\n      })\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const ids = row.id || this.ids\r\n      this.$modal.confirm('是否确认删除分类编号为\"' + ids + '\"的数据项？').then(function() {\r\n        return delCategory(ids)\r\n      }).then(() => {\r\n        this.getList()\r\n        this.$modal.msgSuccess(\"删除成功\")\r\n      }).catch(() => {})\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('biz/category/export', {\r\n        ...this.queryParams\r\n      }, `category_${new Date().getTime()}.xlsx`)\r\n    }\r\n  }\r\n}\r\n</script>\r\n"]}]}