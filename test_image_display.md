# 题库默认图片显示测试

## 修改内容

1. **使用require引入默认图片**：
   ```javascript
   // 默认图片
   defaultImage: require('@/assets/images/default_pic.png'),
   ```

2. **统一图片源处理**：
   ```javascript
   /** 获取图片源地址 */
   getImageSrc(coverImg) {
     if (coverImg && coverImg.trim()) {
       return this.baseUrl + coverImg
     }
     return this.defaultImage
   }
   ```

3. **简化模板代码**：
   ```vue
   <el-image
     :src="getImageSrc(scope.row.coverImg)"
     :preview-src-list="[getImageSrc(scope.row.coverImg)]"
     style="width: 50px; height: 50px; object-fit: cover; cursor: pointer;"
     fit="cover"
     :lazy="true"
   >
     <div slot="error" class="image-slot">
       <i class="el-icon-picture-outline"></i>
     </div>
   </el-image>
   ```

## 测试场景

1. 有封面图片的题库 - 应该显示实际图片
2. 没有封面图片的题库 - 应该显示默认图片
3. 图片加载失败 - 应该显示错误图标

## 优化点

- 使用require确保webpack正确处理图片资源
- 添加懒加载提高性能
- 添加错误处理显示备用图标
- 统一图片源处理逻辑
