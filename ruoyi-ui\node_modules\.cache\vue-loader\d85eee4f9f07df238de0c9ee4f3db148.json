{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\index.vue?vue&type=template&id=ca4f1f4a&scoped=true", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\index.vue", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}