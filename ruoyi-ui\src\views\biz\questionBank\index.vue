<template>
  <div class="app-container">
    <!-- 搜索区域和统计图表 -->
    <el-row :gutter="20">
      <el-col :span="16">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
          <el-form-item label="题库名称" prop="bankName">
            <el-input
              v-model="queryParams.bankName"
              placeholder="请输入题库名称"
              clearable
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="所属分类" prop="categoryId">
            <el-cascader
              v-model="queryParams.categoryId"
              :options="categoryOptions"
              :props="cascaderProps"
              placeholder="请选择分类"
              clearable
              style="width: 200px"
              @change="handleCategoryChange">
            </el-cascader>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            <el-button type="info" icon="el-icon-pie-chart" size="mini" @click="showStatisticsDialog">题型统计</el-button>
          </el-form-item>
        </el-form>
      </el-col>
      <el-col :span="8">
        <!-- 题型统计饼状图 -->
        <el-card class="statistics-card" v-show="showSearch">
          <div slot="header" class="clearfix">
            <span style="font-size: 14px; font-weight: bold;">题型统计</span>
          </div>
          <div ref="pieChart" style="width: 100%; height: 200px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['biz:questionBank:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['biz:questionBank:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['biz:questionBank:remove']"
        >删除</el-button>
      </el-col>
      <!-- <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['biz:questionBank:export']"
        >导出</el-button>
      </el-col> -->
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table border v-loading="loading" :data="questionBankList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="封面图片" align="center" prop="coverImg" width="100" min-width="100">
        <template slot-scope="scope">
          <el-image
            :src="getImageSrc(scope.row.coverImg)"
            :preview-src-list="[getImageSrc(scope.row.coverImg)]"
            style="width: 50px; height: 50px; object-fit: cover; cursor: pointer;"
            fit="cover"
            :lazy="true"
          >
            <div slot="error" class="image-slot">
              <i class="el-icon-picture-outline"></i>
            </div>
          </el-image>
        </template>
      </el-table-column>
      <el-table-column label="题库名称" align="center" prop="bankName" min-width="150">
        <template slot-scope="scope">
          <router-link
            :to="{ path: '/biz/questionBank/detail', query: { bankId: scope.row.bankId, bankName: scope.row.bankName } }"
            class="link-type"
            style="color: #409EFF; text-decoration: none;"
          >
            {{ scope.row.bankName }}
          </router-link>
        </template>
      </el-table-column>
      <el-table-column label="题库描述" align="center" prop="bankDesc" min-width="200" show-overflow-tooltip><
        <template slot-scope="scope">
          <span v-if="scope.row.bankDesc">{{ scope.row.bankDesc }}</span>
          <span v-else>--</span>
        </template>
      </el-table-column>
      <el-table-column label="所属分类" align="center" prop="categoryId" min-width="120">
        <template slot-scope="scope">
          {{ getCategoryName(scope.row.categoryId) }}
        </template>
      </el-table-column>
      <el-table-column label="顺序" align="center" prop="orderNum" width="80" min-width="80" />
      <el-table-column label="状态" align="center" prop="status" width="80" min-width="80">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="0"
            :inactive-value="1"
            @change="handleStatusChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="150" min-width="150" fixed="right" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['biz:questionBank:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['biz:questionBank:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改题库对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="35%" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="分类" prop="categoryId">
          <el-cascader
            v-model="form.categoryId"
            :options="categoryOptions"
            :props="cascaderProps"
            placeholder="请选择分类"
            clearable
            style="width: 100%"
            @change="handleFormCategoryChange">
          </el-cascader>
        </el-form-item>
        <el-form-item label="题库名称" prop="bankName">
          <el-input v-model="form.bankName" placeholder="请输入题库名称" />
        </el-form-item>
        <el-form-item label="题库描述" prop="bankDesc">
          <el-input v-model="form.bankDesc" rows="3" type="textarea" placeholder="请输入题库描述" />
        </el-form-item>
        <el-form-item label="顺序" prop="orderNum">
          <el-input-number v-model="form.orderNum" :min="0" :max="9999" placeholder="请输入顺序" style="width: 100%" />
        </el-form-item>
        <el-form-item label="封面图片" prop="coverImg">
          <image-upload v-model="form.coverImg" :limit="1"/>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 题型统计弹窗 -->
    <el-dialog title="题型统计" :visible.sync="statisticsDialogVisible" width="600px" append-to-body>
      <div class="statistics-dialog-content">
        <div class="statistics-summary">
          <el-row :gutter="20">
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-number">{{ statisticsData.total }}</div>
                <div class="stat-label">总题数</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-number" style="color: #409EFF;">{{ statisticsData.singleChoice }}</div>
                <div class="stat-label">单选题</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-number" style="color: #67C23A;">{{ statisticsData.multipleChoice }}</div>
                <div class="stat-label">多选题</div>
              </div>
            </el-col>
            <el-col :span="6">
              <div class="stat-item">
                <div class="stat-number" style="color: #E6A23C;">{{ statisticsData.judgment }}</div>
                <div class="stat-label">判断题</div>
              </div>
            </el-col>
          </el-row>
        </div>
        <div class="statistics-chart">
          <div ref="dialogPieChart" style="width: 100%; height: 300px;"></div>
        </div>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="statisticsDialogVisible = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listQuestionBank, getQuestionBank, delQuestionBank, addQuestionBank, updateQuestionBank } from "@/api/biz/questionBank"
import { listCategory } from "@/api/biz/category"
import { getQuestionStatistics, getGlobalQuestionStatistics } from "@/api/biz/question"
import * as echarts from 'echarts'

export default {
  name: "QuestionBank",
  data() {
    return {
      // 基础URL
      baseUrl: process.env.VUE_APP_BASE_API,
      // 默认图片
      defaultImage: require('@/assets/images/default_pic.png'),
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 题库表格数据
      questionBankList: [],
      // 分类选项数据
      categoryOptions: [],
      // 饼状图实例
      pieChart: null,
      // 弹窗饼状图实例
      dialogPieChart: null,
      // 统计弹窗显示状态
      statisticsDialogVisible: false,
      // 统计数据
      statisticsData: {
        total: 0,
        singleChoice: 0,
        multipleChoice: 0,
        judgment: 0
      },
      // 级联选择器配置
      cascaderProps: {
        value: 'id',
        label: 'name',
        children: 'children',
        expandTrigger: 'hover',
        emitPath: false // 只返回最后一级的值
      },
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        bankName: null,
        categoryId: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        bankName: [
          { required: true, message: "题库名称不能为空", trigger: "blur" }
        ],
      }
    }
  },
  created() {
    this.getList()
    this.getCategoryList()
    this.getGlobalStatistics()
  },
  mounted() {
    this.$nextTick(() => {
      this.initPieChart()
    })
  },
  beforeDestroy() {
    if (this.pieChart) {
      this.pieChart.dispose()
    }
    if (this.dialogPieChart) {
      this.dialogPieChart.dispose()
    }
  },
  methods: {
    /** 查询题库列表 */
    getList() {
      this.loading = true
      listQuestionBank(this.queryParams).then(response => {
        this.questionBankList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    /** 查询分类列表 */
    getCategoryList() {
      listCategory({ pageSize: 1000 }).then(response => {
        const categories = response.rows || []
        this.categoryOptions = this.buildCategoryTree(categories)
      })
    },
    /** 构建分类树形结构 */
    buildCategoryTree(categories) {
      const map = {}
      const result = []

      // 先将所有分类放入map中
      categories.forEach(category => {
        map[category.id] = { ...category, children: [] }
      })

      // 构建完整的树形结构
      const fullTree = []
      categories.forEach(category => {
        if (category.parentId === 0) {
          // 顶级分类
          fullTree.push(map[category.id])
        } else {
          // 子分类
          if (map[category.parentId]) {
            map[category.parentId].children.push(map[category.id])
          }
        }
      })

      // 清理空的children数组，确保叶子节点没有children属性
      const cleanEmptyChildren = (node) => {
        if (node.children && node.children.length === 0) {
          delete node.children
        } else if (node.children && node.children.length > 0) {
          node.children.forEach(child => cleanEmptyChildren(child))
        }
      }

      // 只返回第二级及以下的分类（跳过第一级）
      fullTree.forEach(firstLevel => {
        if (firstLevel.children && firstLevel.children.length > 0) {
          // 将第二级分类作为顶级显示
          firstLevel.children.forEach(secondLevel => {
            cleanEmptyChildren(secondLevel)
            result.push(secondLevel)
          })
        }
      })

      return result
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    // 表单重置
    reset() {
      this.form = {
        bankId: null,
        orderNum: 0,
        bankName: null,
        bankDesc: null,
        categoryId: null,
        coverImg: null,
        status: null,
        createBy: null,
        createTime: null,
        updateTime: null
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.bankId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset()
      this.open = true
      this.title = "添加题库"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset()
      const bankId = row.bankId || this.ids
      getQuestionBank(bankId).then(response => {
        this.form = response.data
        this.open = true
        this.title = "修改题库"
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.bankId != null) {
            updateQuestionBank(this.form).then(() => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          } else {
            addQuestionBank(this.form).then(() => {
              this.$modal.msgSuccess("新增成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const bankIds = row.bankId || this.ids
      this.$modal.confirm('是否确认删除题库编号为"' + bankIds + '"的数据项？').then(function() {
        return delQuestionBank(bankIds)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {})
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('biz/questionBank/export', {
        ...this.queryParams
      }, `questionBank_${new Date().getTime()}.xlsx`)
    },
    /** 根据分类ID获取分类名称 */
    getCategoryName(categoryId) {
      const category = this.findCategoryById(this.categoryOptions, categoryId)
      return category ? category.name : '未分类'
    },
    /** 在树形结构中查找分类 */
    findCategoryById(categories, id) {
      for (let category of categories) {
        if (category.id === id) {
          return category
        }
        if (category.children && category.children.length > 0) {
          const found = this.findCategoryById(category.children, id)
          if (found) {
            return found
          }
        }
      }
      return null
    },
    /** 搜索分类变化处理 */
    handleCategoryChange(value) {
      this.queryParams.categoryId = value
    },
    /** 表单分类变化处理 */
    handleFormCategoryChange(value) {
      this.form.categoryId = value
    },
    /** 状态变化处理 */
    handleStatusChange(row) {
      const text = row.status === 0 ? "启用" : "禁用"
      this.$modal.confirm('确认要"' + text + '""' + row.bankName + '"题库吗？').then(() => {
        return updateQuestionBank(row)
      }).then(() => {
        this.$modal.msgSuccess(text + "成功")
        this.getList()
      }).catch(() => {
        // 如果取消或失败，恢复原状态
        row.status = row.status === 0 ? 1 : 0
      })
    },
    /** 获取图片源地址 */
    getImageSrc(coverImg) {
      if (coverImg && coverImg.trim()) {
        return this.baseUrl + coverImg
      }
      return this.defaultImage
    },
    /** 获取全局题型统计 */
    getGlobalStatistics() {
      getGlobalQuestionStatistics().then(response => {
        this.statisticsData = response.data || {
          total: 0,
          singleChoice: 0,
          multipleChoice: 0,
          judgment: 0
        }
        this.$nextTick(() => {
          this.updatePieChart()
        })
      }).catch(() => {
        // 如果API调用失败，使用默认数据
        this.statisticsData = {
          total: 0,
          singleChoice: 0,
          multipleChoice: 0,
          judgment: 0
        }
        this.$nextTick(() => {
          this.updatePieChart()
        })
      })
    },
    /** 初始化饼状图 */
    initPieChart() {
      if (this.$refs.pieChart) {
        this.pieChart = echarts.init(this.$refs.pieChart)
        this.updatePieChart()
      }
    },
    /** 更新饼状图数据 */
    updatePieChart() {
      if (!this.pieChart) return

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'horizontal',
          bottom: '0%',
          left: 'center',
          itemWidth: 10,
          itemHeight: 10,
          textStyle: {
            fontSize: 12
          }
        },
        series: [
          {
            name: '题型统计',
            type: 'pie',
            radius: ['30%', '70%'],
            center: ['50%', '45%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '14',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: [
              {
                value: this.statisticsData.singleChoice,
                name: '单选题',
                itemStyle: { color: '#5470c6' }
              },
              {
                value: this.statisticsData.multipleChoice,
                name: '多选题',
                itemStyle: { color: '#91cc75' }
              },
              {
                value: this.statisticsData.judgment,
                name: '判断题',
                itemStyle: { color: '#fac858' }
              }
            ]
          }
        ]
      }

      this.pieChart.setOption(option)
    },
    /** 显示题型统计弹窗 */
    showStatisticsDialog() {
      this.statisticsDialogVisible = true
      this.$nextTick(() => {
        this.initDialogPieChart()
      })
    },
    /** 初始化弹窗饼状图 */
    initDialogPieChart() {
      if (this.$refs.dialogPieChart) {
        this.dialogPieChart = echarts.init(this.$refs.dialogPieChart)
        this.updateDialogPieChart()
      }
    },
    /** 更新弹窗饼状图数据 */
    updateDialogPieChart() {
      if (!this.dialogPieChart) return

      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'horizontal',
          bottom: '10px',
          left: 'center',
          itemWidth: 12,
          itemHeight: 12,
          textStyle: {
            fontSize: 14
          }
        },
        series: [
          {
            name: '题型统计',
            type: 'pie',
            radius: ['40%', '70%'],
            center: ['50%', '45%'],
            avoidLabelOverlap: false,
            label: {
              show: true,
              position: 'outside',
              formatter: '{b}: {c}',
              fontSize: 12
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '16',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: true
            },
            data: [
              {
                value: this.statisticsData.singleChoice,
                name: '单选题',
                itemStyle: { color: '#409EFF' }
              },
              {
                value: this.statisticsData.multipleChoice,
                name: '多选题',
                itemStyle: { color: '#67C23A' }
              },
              {
                value: this.statisticsData.judgment,
                name: '判断题',
                itemStyle: { color: '#E6A23C' }
              }
            ]
          }
        ]
      }

      this.dialogPieChart.setOption(option)
    }
  }
}
</script>

<style scoped>
.statistics-card {
  height: 280px;
}

.statistics-card .el-card__body {
  padding: 10px;
}

.statistics-card .el-card__header {
  padding: 10px 20px;
  border-bottom: 1px solid #ebeef5;
}

/* 统计弹窗样式 */
.statistics-dialog-content {
  padding: 20px 0;
}

.statistics-summary {
  margin-bottom: 30px;
}

.stat-item {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.stat-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

.statistics-chart {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}
</style>
