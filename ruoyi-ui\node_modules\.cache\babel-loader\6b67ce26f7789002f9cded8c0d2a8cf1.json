{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\api\\biz\\question.js", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\api\\biz\\question.js", "mtime": 1754018413621}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\babel.config.js", "mtime": 1750638259743}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listQuestion", "query", "request", "url", "method", "params", "getQuestion", "questionId", "addQuestion", "data", "updateQuestion", "delQuestion", "getQuestionStatistics", "bankId", "getGlobalQuestionStatistics", "batchImportQuestions", "parseImportFile", "downloadTemplate", "questionType", "responseType", "copyQuestion", "exportQuestionsToWord"], "sources": ["D:/IDEA_PROJECT/exam/ruoyi-ui/src/api/biz/question.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询题目列表\r\nexport function listQuestion(query) {\r\n  return request({\r\n    url: '/biz/question/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询题目详细\r\nexport function getQuestion(questionId) {\r\n  return request({\r\n    url: '/biz/question/' + questionId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增题目\r\nexport function addQuestion(data) {\r\n  return request({\r\n    url: '/biz/question',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改题目\r\nexport function updateQuestion(data) {\r\n  return request({\r\n    url: '/biz/question',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除题目\r\nexport function delQuestion(questionId) {\r\n  return request({\r\n    url: '/biz/question/' + questionId,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 获取题库统计信息\r\nexport function getQuestionStatistics(bankId) {\r\n  return request({\r\n    url: '/biz/question/statistics/' + bankId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 获取全局题型统计信息\r\nexport function getGlobalQuestionStatistics() {\r\n  return request({\r\n    url: '/biz/question/globalStatistics',\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 批量导入题目\r\nexport function batchImportQuestions(data) {\r\n  return request({\r\n    url: '/biz/question/batchImport',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 解析导入文件\r\nexport function parseImportFile(data) {\r\n  return request({\r\n    url: '/biz/question/parseFile',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 下载导入模板\r\nexport function downloadTemplate(questionType) {\r\n  return request({\r\n    url: '/biz/question/downloadTemplate',\r\n    method: 'get',\r\n    params: { questionType },\r\n    responseType: 'blob'\r\n  })\r\n}\r\n\r\n// 复制题目\r\nexport function copyQuestion(questionId) {\r\n  return request({\r\n    url: '/biz/question/copy/' + questionId,\r\n    method: 'post'\r\n  })\r\n}\r\n\r\n// 导出题目为Word文档\r\nexport function exportQuestionsToWord(params) {\r\n  return request({\r\n    url: '/biz/question/exportWord',\r\n    method: 'get',\r\n    params: params,\r\n    responseType: 'blob'\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,YAAYA,CAACC,KAAK,EAAE;EAClC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB;IACzBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,WAAWA,CAACC,UAAU,EAAE;EACtC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,gBAAgB,GAAGI,UAAU;IAClCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,WAAWA,CAACC,IAAI,EAAE;EAChC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe;IACpBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,cAAcA,CAACD,IAAI,EAAE;EACnC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe;IACpBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,WAAWA,CAACJ,UAAU,EAAE;EACtC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,gBAAgB,GAAGI,UAAU;IAClCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,qBAAqBA,CAACC,MAAM,EAAE;EAC5C,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAE,2BAA2B,GAAGU,MAAM;IACzCT,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASU,2BAA2BA,CAAA,EAAG;EAC5C,OAAO,IAAAZ,gBAAO,EAAC;IACbC,GAAG,EAAE,gCAAgC;IACrCC,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASW,oBAAoBA,CAACN,IAAI,EAAE;EACzC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,2BAA2B;IAChCC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASO,eAAeA,CAACP,IAAI,EAAE;EACpC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,yBAAyB;IAC9BC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,gBAAgBA,CAACC,YAAY,EAAE;EAC7C,OAAO,IAAAhB,gBAAO,EAAC;IACbC,GAAG,EAAE,gCAAgC;IACrCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAE;MAAEa,YAAY,EAAZA;IAAa,CAAC;IACxBC,YAAY,EAAE;EAChB,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,YAAYA,CAACb,UAAU,EAAE;EACvC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,qBAAqB,GAAGI,UAAU;IACvCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASiB,qBAAqBA,CAAChB,MAAM,EAAE;EAC5C,OAAO,IAAAH,gBAAO,EAAC;IACbC,GAAG,EAAE,0BAA0B;IAC/BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEA,MAAM;IACdc,YAAY,EAAE;EAChB,CAAC,CAAC;AACJ", "ignoreList": []}]}