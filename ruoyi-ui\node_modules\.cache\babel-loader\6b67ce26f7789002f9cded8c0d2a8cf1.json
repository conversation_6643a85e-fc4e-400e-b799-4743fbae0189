{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\api\\biz\\question.js", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\api\\biz\\question.js", "mtime": 1753955705483}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\babel.config.js", "mtime": 1750638259743}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_request", "_interopRequireDefault", "require", "listQuestion", "query", "request", "url", "method", "params", "getQuestion", "questionId", "addQuestion", "data", "updateQuestion", "delQuestion", "getQuestionStatistics", "bankId", "batchImportQuestions", "parseImportFile", "downloadTemplate", "questionType", "responseType", "copyQuestion", "exportQuestionsToWord"], "sources": ["D:/IDEA_PROJECT/exam/ruoyi-ui/src/api/biz/question.js"], "sourcesContent": ["import request from '@/utils/request'\r\n\r\n// 查询题目列表\r\nexport function listQuestion(query) {\r\n  return request({\r\n    url: '/biz/question/list',\r\n    method: 'get',\r\n    params: query\r\n  })\r\n}\r\n\r\n// 查询题目详细\r\nexport function getQuestion(questionId) {\r\n  return request({\r\n    url: '/biz/question/' + questionId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 新增题目\r\nexport function addQuestion(data) {\r\n  return request({\r\n    url: '/biz/question',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 修改题目\r\nexport function updateQuestion(data) {\r\n  return request({\r\n    url: '/biz/question',\r\n    method: 'put',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 删除题目\r\nexport function delQuestion(questionId) {\r\n  return request({\r\n    url: '/biz/question/' + questionId,\r\n    method: 'delete'\r\n  })\r\n}\r\n\r\n// 获取题库统计信息\r\nexport function getQuestionStatistics(bankId) {\r\n  return request({\r\n    url: '/biz/question/statistics/' + bankId,\r\n    method: 'get'\r\n  })\r\n}\r\n\r\n// 批量导入题目\r\nexport function batchImportQuestions(data) {\r\n  return request({\r\n    url: '/biz/question/batchImport',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 解析导入文件\r\nexport function parseImportFile(data) {\r\n  return request({\r\n    url: '/biz/question/parseFile',\r\n    method: 'post',\r\n    data: data\r\n  })\r\n}\r\n\r\n// 下载导入模板\r\nexport function downloadTemplate(questionType) {\r\n  return request({\r\n    url: '/biz/question/downloadTemplate',\r\n    method: 'get',\r\n    params: { questionType },\r\n    responseType: 'blob'\r\n  })\r\n}\r\n\r\n// 复制题目\r\nexport function copyQuestion(questionId) {\r\n  return request({\r\n    url: '/biz/question/copy/' + questionId,\r\n    method: 'post'\r\n  })\r\n}\r\n\r\n// 导出题目为Word文档\r\nexport function exportQuestionsToWord(params) {\r\n  return request({\r\n    url: '/biz/question/exportWord',\r\n    method: 'get',\r\n    params: params,\r\n    responseType: 'blob'\r\n  })\r\n}\r\n"], "mappings": ";;;;;;;;;;;;;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA;AACO,SAASC,YAAYA,CAACC,KAAK,EAAE;EAClC,OAAO,IAAAC,gBAAO,EAAC;IACbC,GAAG,EAAE,oBAAoB;IACzBC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEJ;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASK,WAAWA,CAACC,UAAU,EAAE;EACtC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,gBAAgB,GAAGI,UAAU;IAClCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASI,WAAWA,CAACC,IAAI,EAAE;EAChC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe;IACpBC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,cAAcA,CAACD,IAAI,EAAE;EACnC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,eAAe;IACpBC,MAAM,EAAE,KAAK;IACbK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASE,WAAWA,CAACJ,UAAU,EAAE;EACtC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,gBAAgB,GAAGI,UAAU;IAClCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASQ,qBAAqBA,CAACC,MAAM,EAAE;EAC5C,OAAO,IAAAX,gBAAO,EAAC;IACbC,GAAG,EAAE,2BAA2B,GAAGU,MAAM;IACzCT,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASU,oBAAoBA,CAACL,IAAI,EAAE;EACzC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,2BAA2B;IAChCC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASM,eAAeA,CAACN,IAAI,EAAE;EACpC,OAAO,IAAAP,gBAAO,EAAC;IACbC,GAAG,EAAE,yBAAyB;IAC9BC,MAAM,EAAE,MAAM;IACdK,IAAI,EAAEA;EACR,CAAC,CAAC;AACJ;;AAEA;AACO,SAASO,gBAAgBA,CAACC,YAAY,EAAE;EAC7C,OAAO,IAAAf,gBAAO,EAAC;IACbC,GAAG,EAAE,gCAAgC;IACrCC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAE;MAAEY,YAAY,EAAZA;IAAa,CAAC;IACxBC,YAAY,EAAE;EAChB,CAAC,CAAC;AACJ;;AAEA;AACO,SAASC,YAAYA,CAACZ,UAAU,EAAE;EACvC,OAAO,IAAAL,gBAAO,EAAC;IACbC,GAAG,EAAE,qBAAqB,GAAGI,UAAU;IACvCH,MAAM,EAAE;EACV,CAAC,CAAC;AACJ;;AAEA;AACO,SAASgB,qBAAqBA,CAACf,MAAM,EAAE;EAC5C,OAAO,IAAAH,gBAAO,EAAC;IACbC,GAAG,EAAE,0BAA0B;IAC/BC,MAAM,EAAE,KAAK;IACbC,MAAM,EAAEA,MAAM;IACda,YAAY,EAAE;EAChB,CAAC,CAAC;AACJ", "ignoreList": []}]}