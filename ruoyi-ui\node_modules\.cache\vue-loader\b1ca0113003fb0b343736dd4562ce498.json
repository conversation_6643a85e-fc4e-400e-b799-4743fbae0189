{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\index.vue?vue&type=style&index=0&id=ca4f1f4a&scoped=true&lang=css", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\index.vue", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCg0KLnN0YXRpc3RpY3MtY2FyZCB7DQogIGhlaWdodDogMjgwcHg7DQp9DQoNCi5zdGF0aXN0aWNzLWNhcmQgLmVsLWNhcmRfX2JvZHkgew0KICBwYWRkaW5nOiAxMHB4Ow0KfQ0KDQouc3RhdGlzdGljcy1jYXJkIC5lbC1jYXJkX19oZWFkZXIgew0KICBwYWRkaW5nOiAxMHB4IDIwcHg7DQogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZWJlZWY1Ow0KfQ0K"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwkBA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/biz/questionBank", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 搜索区域和统计图表 -->\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"16\">\r\n        <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n          <el-form-item label=\"题库名称\" prop=\"bankName\">\r\n            <el-input\r\n              v-model=\"queryParams.bankName\"\r\n              placeholder=\"请输入题库名称\"\r\n              clearable\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"所属分类\" prop=\"categoryId\">\r\n            <el-cascader\r\n              v-model=\"queryParams.categoryId\"\r\n              :options=\"categoryOptions\"\r\n              :props=\"cascaderProps\"\r\n              placeholder=\"请选择分类\"\r\n              clearable\r\n              style=\"width: 200px\"\r\n              @change=\"handleCategoryChange\">\r\n            </el-cascader>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-col>\r\n      <el-col :span=\"8\">\r\n        <!-- 题型统计饼状图 -->\r\n        <el-card class=\"statistics-card\" v-show=\"showSearch\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span style=\"font-size: 14px; font-weight: bold;\">题型统计</span>\r\n          </div>\r\n          <div ref=\"pieChart\" style=\"width: 100%; height: 200px;\"></div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['biz:questionBank:add']\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n          v-hasPermi=\"['biz:questionBank:edit']\"\r\n        >修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['biz:questionBank:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <!-- <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['biz:questionBank:export']\"\r\n        >导出</el-button>\r\n      </el-col> -->\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table border v-loading=\"loading\" :data=\"questionBankList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"封面图片\" align=\"center\" prop=\"coverImg\" width=\"100\" min-width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <el-image\r\n            :src=\"getImageSrc(scope.row.coverImg)\"\r\n            :preview-src-list=\"[getImageSrc(scope.row.coverImg)]\"\r\n            style=\"width: 50px; height: 50px; object-fit: cover; cursor: pointer;\"\r\n            fit=\"cover\"\r\n            :lazy=\"true\"\r\n          >\r\n            <div slot=\"error\" class=\"image-slot\">\r\n              <i class=\"el-icon-picture-outline\"></i>\r\n            </div>\r\n          </el-image>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"题库名称\" align=\"center\" prop=\"bankName\" min-width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <router-link\r\n            :to=\"{ path: '/biz/questionBank/detail', query: { bankId: scope.row.bankId, bankName: scope.row.bankName } }\"\r\n            class=\"link-type\"\r\n            style=\"color: #409EFF; text-decoration: none;\"\r\n          >\r\n            {{ scope.row.bankName }}\r\n          </router-link>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"题库描述\" align=\"center\" prop=\"bankDesc\" min-width=\"200\" show-overflow-tooltip><\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.bankDesc\">{{ scope.row.bankDesc }}</span>\r\n          <span v-else>--</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"所属分类\" align=\"center\" prop=\"categoryId\" min-width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          {{ getCategoryName(scope.row.categoryId) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"顺序\" align=\"center\" prop=\"orderNum\" width=\"80\" min-width=\"80\" />\r\n      <el-table-column label=\"状态\" align=\"center\" prop=\"status\" width=\"80\" min-width=\"80\">\r\n        <template slot-scope=\"scope\">\r\n          <el-switch\r\n            v-model=\"scope.row.status\"\r\n            :active-value=\"0\"\r\n            :inactive-value=\"1\"\r\n            @change=\"handleStatusChange(scope.row)\"\r\n          ></el-switch>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" width=\"150\" min-width=\"150\" fixed=\"right\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['biz:questionBank:edit']\"\r\n          >修改</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['biz:questionBank:remove']\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    \r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加或修改题库对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"35%\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-form-item label=\"分类\" prop=\"categoryId\">\r\n          <el-cascader\r\n            v-model=\"form.categoryId\"\r\n            :options=\"categoryOptions\"\r\n            :props=\"cascaderProps\"\r\n            placeholder=\"请选择分类\"\r\n            clearable\r\n            style=\"width: 100%\"\r\n            @change=\"handleFormCategoryChange\">\r\n          </el-cascader>\r\n        </el-form-item>\r\n        <el-form-item label=\"题库名称\" prop=\"bankName\">\r\n          <el-input v-model=\"form.bankName\" placeholder=\"请输入题库名称\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"题库描述\" prop=\"bankDesc\">\r\n          <el-input v-model=\"form.bankDesc\" rows=\"3\" type=\"textarea\" placeholder=\"请输入题库描述\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"顺序\" prop=\"orderNum\">\r\n          <el-input-number v-model=\"form.orderNum\" :min=\"0\" :max=\"9999\" placeholder=\"请输入顺序\" style=\"width: 100%\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"封面图片\" prop=\"coverImg\">\r\n          <image-upload v-model=\"form.coverImg\" :limit=\"1\"/>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listQuestionBank, getQuestionBank, delQuestionBank, addQuestionBank, updateQuestionBank } from \"@/api/biz/questionBank\"\r\nimport { listCategory } from \"@/api/biz/category\"\r\nimport { getQuestionStatistics, getGlobalQuestionStatistics } from \"@/api/biz/question\"\r\nimport * as echarts from 'echarts'\r\n\r\nexport default {\r\n  name: \"QuestionBank\",\r\n  data() {\r\n    return {\r\n      // 基础URL\r\n      baseUrl: process.env.VUE_APP_BASE_API,\r\n      // 默认图片\r\n      defaultImage: require('@/assets/images/default_pic.png'),\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 题库表格数据\r\n      questionBankList: [],\r\n      // 分类选项数据\r\n      categoryOptions: [],\r\n      // 饼状图实例\r\n      pieChart: null,\r\n      // 统计数据\r\n      statisticsData: {\r\n        total: 0,\r\n        singleChoice: 0,\r\n        multipleChoice: 0,\r\n        judgment: 0\r\n      },\r\n      // 级联选择器配置\r\n      cascaderProps: {\r\n        value: 'id',\r\n        label: 'name',\r\n        children: 'children',\r\n        expandTrigger: 'hover',\r\n        emitPath: false // 只返回最后一级的值\r\n      },\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        bankName: null,\r\n        categoryId: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        bankName: [\r\n          { required: true, message: \"题库名称不能为空\", trigger: \"blur\" }\r\n        ],\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n    this.getCategoryList()\r\n    this.getGlobalStatistics()\r\n  },\r\n  mounted() {\r\n    this.$nextTick(() => {\r\n      this.initPieChart()\r\n    })\r\n  },\r\n  beforeDestroy() {\r\n    if (this.pieChart) {\r\n      this.pieChart.dispose()\r\n    }\r\n  },\r\n  methods: {\r\n    /** 查询题库列表 */\r\n    getList() {\r\n      this.loading = true\r\n      listQuestionBank(this.queryParams).then(response => {\r\n        this.questionBankList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n      })\r\n    },\r\n    /** 查询分类列表 */\r\n    getCategoryList() {\r\n      listCategory({ pageSize: 1000 }).then(response => {\r\n        const categories = response.rows || []\r\n        this.categoryOptions = this.buildCategoryTree(categories)\r\n      })\r\n    },\r\n    /** 构建分类树形结构 */\r\n    buildCategoryTree(categories) {\r\n      const map = {}\r\n      const result = []\r\n\r\n      // 先将所有分类放入map中\r\n      categories.forEach(category => {\r\n        map[category.id] = { ...category, children: [] }\r\n      })\r\n\r\n      // 构建完整的树形结构\r\n      const fullTree = []\r\n      categories.forEach(category => {\r\n        if (category.parentId === 0) {\r\n          // 顶级分类\r\n          fullTree.push(map[category.id])\r\n        } else {\r\n          // 子分类\r\n          if (map[category.parentId]) {\r\n            map[category.parentId].children.push(map[category.id])\r\n          }\r\n        }\r\n      })\r\n\r\n      // 清理空的children数组，确保叶子节点没有children属性\r\n      const cleanEmptyChildren = (node) => {\r\n        if (node.children && node.children.length === 0) {\r\n          delete node.children\r\n        } else if (node.children && node.children.length > 0) {\r\n          node.children.forEach(child => cleanEmptyChildren(child))\r\n        }\r\n      }\r\n\r\n      // 只返回第二级及以下的分类（跳过第一级）\r\n      fullTree.forEach(firstLevel => {\r\n        if (firstLevel.children && firstLevel.children.length > 0) {\r\n          // 将第二级分类作为顶级显示\r\n          firstLevel.children.forEach(secondLevel => {\r\n            cleanEmptyChildren(secondLevel)\r\n            result.push(secondLevel)\r\n          })\r\n        }\r\n      })\r\n\r\n      return result\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.reset()\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        bankId: null,\r\n        orderNum: 0,\r\n        bankName: null,\r\n        bankDesc: null,\r\n        categoryId: null,\r\n        coverImg: null,\r\n        status: null,\r\n        createBy: null,\r\n        createTime: null,\r\n        updateTime: null\r\n      }\r\n      this.resetForm(\"form\")\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\")\r\n      this.handleQuery()\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.bankId)\r\n      this.single = selection.length!==1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset()\r\n      this.open = true\r\n      this.title = \"添加题库\"\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset()\r\n      const bankId = row.bankId || this.ids\r\n      getQuestionBank(bankId).then(response => {\r\n        this.form = response.data\r\n        this.open = true\r\n        this.title = \"修改题库\"\r\n      })\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.bankId != null) {\r\n            updateQuestionBank(this.form).then(() => {\r\n              this.$modal.msgSuccess(\"修改成功\")\r\n              this.open = false\r\n              this.getList()\r\n            })\r\n          } else {\r\n            addQuestionBank(this.form).then(() => {\r\n              this.$modal.msgSuccess(\"新增成功\")\r\n              this.open = false\r\n              this.getList()\r\n            })\r\n          }\r\n        }\r\n      })\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const bankIds = row.bankId || this.ids\r\n      this.$modal.confirm('是否确认删除题库编号为\"' + bankIds + '\"的数据项？').then(function() {\r\n        return delQuestionBank(bankIds)\r\n      }).then(() => {\r\n        this.getList()\r\n        this.$modal.msgSuccess(\"删除成功\")\r\n      }).catch(() => {})\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('biz/questionBank/export', {\r\n        ...this.queryParams\r\n      }, `questionBank_${new Date().getTime()}.xlsx`)\r\n    },\r\n    /** 根据分类ID获取分类名称 */\r\n    getCategoryName(categoryId) {\r\n      const category = this.findCategoryById(this.categoryOptions, categoryId)\r\n      return category ? category.name : '未分类'\r\n    },\r\n    /** 在树形结构中查找分类 */\r\n    findCategoryById(categories, id) {\r\n      for (let category of categories) {\r\n        if (category.id === id) {\r\n          return category\r\n        }\r\n        if (category.children && category.children.length > 0) {\r\n          const found = this.findCategoryById(category.children, id)\r\n          if (found) {\r\n            return found\r\n          }\r\n        }\r\n      }\r\n      return null\r\n    },\r\n    /** 搜索分类变化处理 */\r\n    handleCategoryChange(value) {\r\n      this.queryParams.categoryId = value\r\n    },\r\n    /** 表单分类变化处理 */\r\n    handleFormCategoryChange(value) {\r\n      this.form.categoryId = value\r\n    },\r\n    /** 状态变化处理 */\r\n    handleStatusChange(row) {\r\n      const text = row.status === 0 ? \"启用\" : \"禁用\"\r\n      this.$modal.confirm('确认要\"' + text + '\"\"' + row.bankName + '\"题库吗？').then(() => {\r\n        return updateQuestionBank(row)\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(text + \"成功\")\r\n        this.getList()\r\n      }).catch(() => {\r\n        // 如果取消或失败，恢复原状态\r\n        row.status = row.status === 0 ? 1 : 0\r\n      })\r\n    },\r\n    /** 获取图片源地址 */\r\n    getImageSrc(coverImg) {\r\n      if (coverImg && coverImg.trim()) {\r\n        return this.baseUrl + coverImg\r\n      }\r\n      return this.defaultImage\r\n    },\r\n    /** 获取全局题型统计 */\r\n    getGlobalStatistics() {\r\n      getGlobalQuestionStatistics().then(response => {\r\n        this.statisticsData = response.data || {\r\n          total: 0,\r\n          singleChoice: 0,\r\n          multipleChoice: 0,\r\n          judgment: 0\r\n        }\r\n        this.$nextTick(() => {\r\n          this.updatePieChart()\r\n        })\r\n      }).catch(() => {\r\n        // 如果API调用失败，使用默认数据\r\n        this.statisticsData = {\r\n          total: 0,\r\n          singleChoice: 0,\r\n          multipleChoice: 0,\r\n          judgment: 0\r\n        }\r\n        this.$nextTick(() => {\r\n          this.updatePieChart()\r\n        })\r\n      })\r\n    },\r\n    /** 初始化饼状图 */\r\n    initPieChart() {\r\n      if (this.$refs.pieChart) {\r\n        this.pieChart = echarts.init(this.$refs.pieChart)\r\n        this.updatePieChart()\r\n      }\r\n    },\r\n    /** 更新饼状图数据 */\r\n    updatePieChart() {\r\n      if (!this.pieChart) return\r\n\r\n      const option = {\r\n        tooltip: {\r\n          trigger: 'item',\r\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\r\n        },\r\n        legend: {\r\n          orient: 'horizontal',\r\n          bottom: '0%',\r\n          left: 'center',\r\n          itemWidth: 10,\r\n          itemHeight: 10,\r\n          textStyle: {\r\n            fontSize: 12\r\n          }\r\n        },\r\n        series: [\r\n          {\r\n            name: '题型统计',\r\n            type: 'pie',\r\n            radius: ['30%', '70%'],\r\n            center: ['50%', '45%'],\r\n            avoidLabelOverlap: false,\r\n            label: {\r\n              show: false,\r\n              position: 'center'\r\n            },\r\n            emphasis: {\r\n              label: {\r\n                show: true,\r\n                fontSize: '14',\r\n                fontWeight: 'bold'\r\n              }\r\n            },\r\n            labelLine: {\r\n              show: false\r\n            },\r\n            data: [\r\n              {\r\n                value: this.statisticsData.singleChoice,\r\n                name: '单选题',\r\n                itemStyle: { color: '#5470c6' }\r\n              },\r\n              {\r\n                value: this.statisticsData.multipleChoice,\r\n                name: '多选题',\r\n                itemStyle: { color: '#91cc75' }\r\n              },\r\n              {\r\n                value: this.statisticsData.judgment,\r\n                name: '判断题',\r\n                itemStyle: { color: '#fac858' }\r\n              }\r\n            ]\r\n          }\r\n        ]\r\n      }\r\n\r\n      this.pieChart.setOption(option)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.statistics-card {\r\n  height: 280px;\r\n}\r\n\r\n.statistics-card .el-card__body {\r\n  padding: 10px;\r\n}\r\n\r\n.statistics-card .el-card__header {\r\n  padding: 10px 20px;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n</style>\r\n"]}]}