{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\detail.vue?vue&type=style&index=0&id=5888aa98&scoped=true&lang=css", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\detail.vue", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoucGFnZS1oZWFkZXIgewogIG1hcmdpbi1ib3R0b206IDIwcHg7CiAgcGFkZGluZzogMjBweDsKICBiYWNrZ3JvdW5kOiAjZmZmOwogIGJvcmRlci1yYWRpdXM6IDRweDsKICBib3gtc2hhZG93OiAwIDJweCA0cHggcmdiYSgwLDAsMCwwLjEpOwp9CgouaGVhZGVyLXRpdGxlIHsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgbWFyZ2luLWJvdHRvbTogMjBweDsKfQoKLmhlYWRlci1jb250ZW50IHsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIGdhcDogMjBweDsKICBtaW4taGVpZ2h0OiAzMnB4Owp9Cgouc2VhcmNoLXNlY3Rpb24gewogIGZsZXg6IDE7Cn0KCi5zZWFyY2gtc2VjdGlvbiAuZWwtZm9ybSB7CiAgbWFyZ2luLWJvdHRvbTogMDsKfQoKLnNlYXJjaC1zZWN0aW9uIC5lbC1mb3JtLWl0ZW0gewogIG1hcmdpbi1ib3R0b206IDA7Cn0KCi5zdGF0cy1zZWN0aW9uIHsKICBmbGV4LXNocmluazogMDsKICBwYWRkaW5nLXRvcDogMDsKICBkaXNwbGF5OiBmbGV4OwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAgaGVpZ2h0OiAzMnB4Owp9Cgouc3RhdHMtY29udGFpbmVyIHsKICBkaXNwbGF5OiBmbGV4OwogIGdhcDogMjBweDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIGhlaWdodDogMzJweDsKfQoKLnN0YXQtaXRlbSB7CiAgZGlzcGxheTogZmxleDsKICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CiAgbWluLXdpZHRoOiA2MHB4OwogIGhlaWdodDogMTAwJTsKfQoKLnN0YXQtbGFiZWwgewogIGZvbnQtc2l6ZTogMTJweDsKICBjb2xvcjogIzY2NjsKICBsaW5lLWhlaWdodDogMTsKICBtYXJnaW4tYm90dG9tOiAycHg7Cn0KCi5zdGF0LXZhbHVlIHsKICBmb250LXNpemU6IDE2cHg7CiAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgY29sb3I6ICM0MDlFRkY7CiAgbGluZS1oZWlnaHQ6IDE7Cn0KCi5vcGVyYXRpb24tYmFyIHsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIG1hcmdpbi1ib3R0b206IDE2cHg7CiAgcGFkZGluZzogMTBweCAyMHB4OwogIGJhY2tncm91bmQ6ICNmZmY7CiAgYm9yZGVyLXJhZGl1czogNHB4OwogIGJveC1zaGFkb3c6IDAgMnB4IDRweCByZ2JhKDAsMCwwLDAuMSk7Cn0KCgoKLnF1ZXN0aW9uLWxpc3QgewogIG1pbi1oZWlnaHQ6IDQwMHB4OwogIHBhZGRpbmctYm90dG9tOiAyMHB4OyAvKiDkuLrmnIDlkI7kuIDkuKrpopjnm67mt7vliqDlupXpg6jovrnot50gKi8KfQoKCgouZW1wdHktc3RhdGUgewogIHRleHQtYWxpZ246IGNlbnRlcjsKICBwYWRkaW5nOiA1MHB4IDA7Cn0KCi8qIOaJuemHj+WvvOWFpeaKveWxieagt+W8jyAqLwouYmF0Y2gtaW1wb3J0LWRyYXdlciAuZWwtZHJhd2VyX19ib2R5IHsKICBwYWRkaW5nOiAwOwogIGhlaWdodDogMTAwJTsKfQoKLm1haW4gewogIGhlaWdodDogMTAwJTsKICBtYXJnaW46IDA7Cn0KCi5jb2wtbGVmdCwgLmNvbC1yaWdodCB7CiAgaGVpZ2h0OiAxMDAlOwogIHBhZGRpbmc6IDAgNXB4Owp9CgouaDEwMHAgewogIGhlaWdodDogMTAwJTsKfQoKLnRvb2xiYXIgewogIHBhZGRpbmc6IDE1cHggMjBweDsKICBiYWNrZ3JvdW5kOiAjZjhmOWZhOwogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZTRlN2VkOwogIGRpc3BsYXk6IGZsZXg7CiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuOwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7Cn0KCi50b29sYmFyIC5vcmFuZ2UgewogIGNvbG9yOiAjZTZhMjNjOwogIGZvbnQtc2l6ZTogMTRweDsKfQoKLnRvb2xiYXIgLmZyIHsKICBkaXNwbGF5OiBmbGV4OwogIGdhcDogMTBweDsKfQoKLmNsZWFyZml4OjphZnRlciB7CiAgY29udGVudDogIiI7CiAgZGlzcGxheTogdGFibGU7CiAgY2xlYXI6IGJvdGg7Cn0KCi5mciB7CiAgZmxvYXQ6IHJpZ2h0Owp9CgouZWRpdG9yLXdyYXBwZXIgewogIGZsZXg6IDE7CiAgaGVpZ2h0OiBjYWxjKDEwMHZoIC0gMTYwcHgpOyAvKiDlhajlsY/pq5jluqblh4/ljrvlpLTpg6jlr7zoiKrlkozlhbbku5blhYPntKAgKi8KICBwYWRkaW5nOiAwOwp9CgoucmljaC1lZGl0b3ItY29udGFpbmVyIHsKICBoZWlnaHQ6IDEwMCU7CiAgd2lkdGg6IDEwMCU7Cn0KCi5yaWNoLWVkaXRvci1jb250YWluZXIgLmNrZSB7CiAgaGVpZ2h0OiAxMDAlICFpbXBvcnRhbnQ7Cn0KCi5yaWNoLWVkaXRvci1jb250YWluZXIgLmNrZV9jb250ZW50cyB7CiAgaGVpZ2h0OiBjYWxjKDEwMCUgLSA4MHB4KSAhaW1wb3J0YW50OyAvKiDlh4/ljrvlt6XlhbfmoI/lkozlupXpg6jnirbmgIHmoI/nmoTpq5jluqYgKi8KfQoKLyog5bel5YW35qCP5qC35byP5LyY5YyWICovCi5yaWNoLWVkaXRvci1jb250YWluZXIgLmNrZV90b3AgewogIGJhY2tncm91bmQ6ICNmNWY1ZjUgIWltcG9ydGFudDsKICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2RkZCAhaW1wb3J0YW50OwogIHBhZGRpbmc6IDZweCAhaW1wb3J0YW50Owp9CgoucmljaC1lZGl0b3ItY29udGFpbmVyIC5ja2VfdG9vbGJveCB7CiAgYmFja2dyb3VuZDogdHJhbnNwYXJlbnQgIWltcG9ydGFudDsKfQoKLnJpY2gtZWRpdG9yLWNvbnRhaW5lciAuY2tlX3Rvb2xiYXIgewogIGJhY2tncm91bmQ6IHRyYW5zcGFyZW50ICFpbXBvcnRhbnQ7CiAgYm9yZGVyOiBub25lICFpbXBvcnRhbnQ7CiAgbWFyZ2luOiAycHggNHB4ICFpbXBvcnRhbnQ7CiAgZmxvYXQ6IGxlZnQgIWltcG9ydGFudDsKfQoKLnJpY2gtZWRpdG9yLWNvbnRhaW5lciAuY2tlX2J1dHRvbiB7CiAgYmFja2dyb3VuZDogdHJhbnNwYXJlbnQgIWltcG9ydGFudDsKICBib3JkZXI6IDFweCBzb2xpZCB0cmFuc3BhcmVudCAhaW1wb3J0YW50OwogIGJvcmRlci1yYWRpdXM6IDNweCAhaW1wb3J0YW50OwogIG1hcmdpbjogMXB4ICFpbXBvcnRhbnQ7Cn0KCi5yaWNoLWVkaXRvci1jb250YWluZXIgLmNrZV9idXR0b246aG92ZXIgewogIGJhY2tncm91bmQ6ICNlNmU2ZTYgIWltcG9ydGFudDsKICBib3JkZXItY29sb3I6ICNjY2MgIWltcG9ydGFudDsKfQoKLnJpY2gtZWRpdG9yLWNvbnRhaW5lciAuY2tlX2J1dHRvbl9vbiB7CiAgYmFja2dyb3VuZDogI2Q0ZWRmZCAhaW1wb3J0YW50OwogIGJvcmRlci1jb2xvcjogIzY2YWZlOSAhaW1wb3J0YW50Owp9CgovKiDkuIvmi4noj5zljZXmoLflvI8gKi8KLnJpY2gtZWRpdG9yLWNvbnRhaW5lciAuY2tlX2NvbWJvIHsKICBiYWNrZ3JvdW5kOiB0cmFuc3BhcmVudCAhaW1wb3J0YW50OwogIGJvcmRlcjogMXB4IHNvbGlkIHRyYW5zcGFyZW50ICFpbXBvcnRhbnQ7CiAgYm9yZGVyLXJhZGl1czogM3B4ICFpbXBvcnRhbnQ7CiAgbWFyZ2luOiAxcHggIWltcG9ydGFudDsKfQoKLnJpY2gtZWRpdG9yLWNvbnRhaW5lciAuY2tlX2NvbWJvOmhvdmVyIHsKICBiYWNrZ3JvdW5kOiAjZTZlNmU2ICFpbXBvcnRhbnQ7CiAgYm9yZGVyLWNvbG9yOiAjY2NjICFpbXBvcnRhbnQ7Cn0KCi5yaWNoLWVkaXRvci1jb250YWluZXIgLmNrZV9jb21ib19idXR0b24gewogIGJhY2tncm91bmQ6IHRyYW5zcGFyZW50ICFpbXBvcnRhbnQ7CiAgYm9yZGVyOiBub25lICFpbXBvcnRhbnQ7CiAgcGFkZGluZzogNHB4IDhweCAhaW1wb3J0YW50Owp9CgovKiDlt6XlhbfmoI/liIbnu4TmoLflvI8gKi8KLnJpY2gtZWRpdG9yLWNvbnRhaW5lciAuY2tlX3Rvb2xncm91cCB7CiAgYmFja2dyb3VuZDogdHJhbnNwYXJlbnQgIWltcG9ydGFudDsKICBib3JkZXI6IDFweCBzb2xpZCAjZGRkICFpbXBvcnRhbnQ7CiAgYm9yZGVyLXJhZGl1czogNHB4ICFpbXBvcnRhbnQ7CiAgbWFyZ2luOiAycHggIWltcG9ydGFudDsKICBwYWRkaW5nOiAxcHggIWltcG9ydGFudDsKfQoKLyog5Zu+5YOP55u45YWz5qC35byPICovCi5yaWNoLWVkaXRvci1jb250YWluZXIgaW1nIHsKICBtYXgtd2lkdGg6IDEwMCUgIWltcG9ydGFudDsKICBoZWlnaHQ6IGF1dG8gIWltcG9ydGFudDsKICBib3JkZXItcmFkaXVzOiA0cHggIWltcG9ydGFudDsKICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgwLDAsMCwwLjEpICFpbXBvcnRhbnQ7CiAgbWFyZ2luOiAxMHB4IDAgIWltcG9ydGFudDsKfQoKLnJpY2gtZWRpdG9yLWNvbnRhaW5lciAuY2tlX2RpYWxvZyB7CiAgei1pbmRleDogMTAwMDAgIWltcG9ydGFudDsKfQoKLnJpY2gtZWRpdG9yLWNvbnRhaW5lciAuY2tlX2RpYWxvZ19iYWNrZ3JvdW5kX2NvdmVyIHsKICB6LWluZGV4OiA5OTk5ICFpbXBvcnRhbnQ7Cn0KCi5mYWxsYmFjay10ZXh0YXJlYSB7CiAgd2lkdGg6IDEwMCU7CiAgaGVpZ2h0OiAxMDAlOwogIGJvcmRlcjogbm9uZTsKICBib3JkZXItcmFkaXVzOiAwOwogIHJlc2l6ZTogbm9uZTsKICBmb250LWZhbWlseTogJ0NvdXJpZXIgTmV3JywgbW9ub3NwYWNlOwogIGZvbnQtc2l6ZTogMTRweDsKICBsaW5lLWhlaWdodDogMS42OwogIHBhZGRpbmc6IDIwcHg7CiAgb3V0bGluZTogbm9uZTsKfQoKLmRvY3VtZW50LXRleHRhcmVhIHsKICBoZWlnaHQ6IDEwMCUgIWltcG9ydGFudDsKfQoKLmRvY3VtZW50LXRleHRhcmVhIC5lbC10ZXh0YXJlYV9faW5uZXIgewogIGhlaWdodDogMTAwJSAhaW1wb3J0YW50OwogIGJvcmRlcjogbm9uZTsKICBib3JkZXItcmFkaXVzOiAwOwogIHJlc2l6ZTogbm9uZTsKICBmb250LWZhbWlseTogJ0NvdXJpZXIgTmV3JywgbW9ub3NwYWNlOwogIGZvbnQtc2l6ZTogMTRweDsKICBsaW5lLWhlaWdodDogMS42OwogIHBhZGRpbmc6IDIwcHg7Cn0KCi5jaGVja2FyZWEgewogIHBhZGRpbmc6IDE1cHggMjBweDsKICBiYWNrZ3JvdW5kOiAjZjhmOWZhOwogIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZTRlN2VkOwp9CgouY2hlY2thcmVhIC50aXRsZSB7CiAgZm9udC1zaXplOiAxNnB4OwogIGZvbnQtd2VpZ2h0OiBib2xkOwogIGNvbG9yOiAjMzMzOwogIG1hcmdpbi1yaWdodDogMTVweDsKfQoKLmNoZWNrYXJlYSAuZ3JlZW4gewogIGNvbG9yOiAjNjdjMjNhOwogIG1hcmdpbi1yaWdodDogMTVweDsKfQoKLmNoZWNrYXJlYSAucmVkIHsKICBjb2xvcjogI2Y1NmM2YzsKICBtYXJnaW4tcmlnaHQ6IDE1cHg7Cn0KCi5jaGVja2FyZWEgLm1yMjAgewogIG1hcmdpbi1yaWdodDogMjBweDsKfQoKLnByZXZpZXctd3JhcHBlciB7CiAgZmxleDogMTsKICBoZWlnaHQ6IGNhbGMoMTAwJSAtIDEyMHB4KTsKICBvdmVyZmxvdzogaGlkZGVuOwp9CgoucHJldmlldy1zY3JvbGwtd3JhcHBlciB7CiAgaGVpZ2h0OiAxMDAlOwogIG92ZXJmbG93LXk6IGF1dG87CiAgcGFkZGluZzogMTBweDsKfQoKLmVtcHR5LXJlc3VsdCB7CiAgZGlzcGxheTogZmxleDsKICBmbGV4LWRpcmVjdGlvbjogY29sdW1uOwogIGFsaWduLWl0ZW1zOiBjZW50ZXI7CiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7CiAgaGVpZ2h0OiAyMDBweDsKICBjb2xvcjogIzk5OTsKfQoKLmVtcHR5LXJlc3VsdCBpIHsKICBmb250LXNpemU6IDQ4cHg7CiAgbWFyZ2luLWJvdHRvbTogMTZweDsKfQoKLmVtcHR5LXJlc3VsdCAudGlwIHsKICBmb250LXNpemU6IDEycHg7CiAgY29sb3I6ICNjY2M7Cn0KCi5xdWVzdGlvbi1pdGVtIHsKICBtYXJnaW4tYm90dG9tOiAyMHB4OwogIGJvcmRlcjogMXB4IHNvbGlkICNlNGU3ZWQ7CiAgYm9yZGVyLXJhZGl1czogNHB4OwogIGJhY2tncm91bmQ6ICNmZmY7Cn0KCi5xdWVzdGlvbi1pdGVtIC5lbC1jYXJkX19ib2R5IHsKICBwYWRkaW5nOiAxMHB4IDIwcHggMTVweCAyMHB4Owp9CgoucXVlc3Rpb24tdG9wLWJhciB7CiAgZGlzcGxheTogZmxleDsKICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBtYXJnaW4tYm90dG9tOiAxMHB4Owp9CgoucXVlc3Rpb24tdG9wLWJhciAubGVmdCBmb250IHsKICBmb250LXdlaWdodDogYm9sZDsKICBjb2xvcjogIzMzMzsKfQoKLnF1ZXN0aW9uLWNvbnRlbnQgewogIG1hcmdpbi10b3A6IDEwcHg7Cn0KCi8qIOmimOW5suaYvuekuiAqLwoucXVlc3Rpb24tbWFpbi1saW5lIHsKICBtYXJnaW4tYm90dG9tOiA4cHg7Cn0KCi5xdWVzdGlvbi1tYWluLWxpbmUgLmRpc3BsYXktbGF0ZXggewogIG1hcmdpbjogMDsKfQoKLmRpc3BsYXktbGF0ZXggewogIGZvbnQtc2l6ZTogMTRweDsKICBsaW5lLWhlaWdodDogMS42OwogIGNvbG9yOiAjMzMzOwp9CgovKiDlr4zmlofmnKzmoLzlvI/mlK/mjIEgKi8KLnJpY2gtdGV4dCB7CiAgLyog5Yqg57KXICovCiAgZm9udC13ZWlnaHQ6IG5vcm1hbDsKfQoKLnJpY2gtdGV4dCBzdHJvbmcsCi5yaWNoLXRleHQgYiB7CiAgZm9udC13ZWlnaHQ6IGJvbGQ7CiAgY29sb3I6ICMyYzNlNTA7Cn0KCi5yaWNoLXRleHQgZW0sCi5yaWNoLXRleHQgaSB7CiAgZm9udC1zdHlsZTogaXRhbGljOwogIGNvbG9yOiAjMzQ0OTVlOwp9CgoucmljaC10ZXh0IHUgewogIHRleHQtZGVjb3JhdGlvbjogdW5kZXJsaW5lOwp9CgoucmljaC10ZXh0IHMsCi5yaWNoLXRleHQgc3RyaWtlIHsKICB0ZXh0LWRlY29yYXRpb246IGxpbmUtdGhyb3VnaDsKfQoKLnJpY2gtdGV4dCBwIHsKICBtYXJnaW46IDhweCAwOwogIGxpbmUtaGVpZ2h0OiAxLjY7Cn0KCi5yaWNoLXRleHQgYnIgewogIGxpbmUtaGVpZ2h0OiAxLjY7Cn0KCi8qIOehruS/nUhUTUzlhoXlrrnmraPnoa7mmL7npLogKi8KLnJpY2gtdGV4dCAqIHsKICBtYXgtd2lkdGg6IDEwMCU7Cn0KCi5yaWNoLXRleHQgewogIHdvcmQtd3JhcDogYnJlYWstd29yZDsKfQoKLnF1ZXN0aW9uLW9wdGlvbnMgewogIG1hcmdpbjogNHB4IDAgOHB4IDA7Cn0KCi5vcHRpb24taXRlbSB7CiAgcGFkZGluZzogMnB4IDA7CiAgcGFkZGluZy1sZWZ0OiAxMHB4OwogIGZvbnQtc2l6ZTogMTNweDsKICBjb2xvcjogIzY2NjsKICBsaW5lLWhlaWdodDogMS4zOwp9CgoKCi8qIOaWh+aho+S4iuS8oOWvueivneahhuagt+W8jyAqLwouZG9jdW1lbnQtdXBsb2FkLWRpYWxvZyAuc3VidGl0bGUgewogIGNvbG9yOiAjNDA5ZWZmOwogIGZvbnQtc2l6ZTogMTRweDsKICBtYXJnaW46IDEwcHggMDsKfQoKLmRvY3VtZW50LXVwbG9hZC1kaWFsb2cgLmVsLWJ1dHRvbi0tc21hbGwgewogIG1hcmdpbjogMCA1cHg7Cn0KCi5kb2N1bWVudC11cGxvYWQtZGlhbG9nIC5lbC11cGxvYWQtZHJhZ2dlciB7CiAgd2lkdGg6IDEwMCU7CiAgaGVpZ2h0OiAxMjBweDsKICBib3JkZXI6IDJweCBkYXNoZWQgI2Q5ZDlkOTsKICBib3JkZXItcmFkaXVzOiA2cHg7CiAgY3Vyc29yOiBwb2ludGVyOwogIHBvc2l0aW9uOiByZWxhdGl2ZTsKICBvdmVyZmxvdzogaGlkZGVuOwogIHRyYW5zaXRpb246IGJvcmRlci1jb2xvciAwLjJzIGN1YmljLWJlemllcigwLjY0NSwgMC4wNDUsIDAuMzU1LCAxKTsKfQoKLmRvY3VtZW50LXVwbG9hZC1kaWFsb2cgLmVsLXVwbG9hZC1kcmFnZ2VyOmhvdmVyIHsKICBib3JkZXItY29sb3I6ICM0MDllZmY7Cn0KCi5kb2N1bWVudC11cGxvYWQtZGlhbG9nIC5lbC11cGxvYWQtZHJhZ2dlciAuZWwtaWNvbi11cGxvYWQgewogIGZvbnQtc2l6ZTogNjdweDsKICBjb2xvcjogI2MwYzRjYzsKICBtYXJnaW46IDIwcHggMCAxNnB4OwogIGxpbmUtaGVpZ2h0OiA1MHB4Owp9CgouZG9jdW1lbnQtdXBsb2FkLWRpYWxvZyAuZWwtdXBsb2FkX190ZXh0IHsKICBjb2xvcjogIzYwNjI2NjsKICBmb250LXNpemU6IDE0cHg7CiAgdGV4dC1hbGlnbjogY2VudGVyOwp9CgouZG9jdW1lbnQtdXBsb2FkLWRpYWxvZyAuZWwtdXBsb2FkX190ZXh0IGVtIHsKICBjb2xvcjogIzQwOWVmZjsKICBmb250LXN0eWxlOiBub3JtYWw7Cn0KCi8qIOS4iuS8oOWKoOi9veWKqOeUu+agt+W8jyAqLwoudXBsb2FkLWxvYWRpbmcgewogIHBhZGRpbmc6IDQwcHggMDsKICBjb2xvcjogIzQwOUVGRjsKfQoKLnVwbG9hZC1sb2FkaW5nIC5lbC1pY29uLWxvYWRpbmcgewogIGZvbnQtc2l6ZTogMjhweDsKICBhbmltYXRpb246IHJvdGF0aW5nIDJzIGxpbmVhciBpbmZpbml0ZTsKICBtYXJnaW4tYm90dG9tOiAxMHB4Owp9CgoudXBsb2FkLWxvYWRpbmcgLmVsLXVwbG9hZF9fdGV4dCB7CiAgY29sb3I6ICM0MDlFRkY7CiAgZm9udC1zaXplOiAxNHB4Owp9CgpAa2V5ZnJhbWVzIHJvdGF0aW5nIHsKICAwJSB7CiAgICB0cmFuc2Zvcm06IHJvdGF0ZSgwZGVnKTsKICB9CiAgMTAwJSB7CiAgICB0cmFuc2Zvcm06IHJvdGF0ZSgzNjBkZWcpOwogIH0KfQoKLnJ1bGVzLWRpYWxvZyAucnVsZXMtY29udGVudCB7CiAgbWF4LWhlaWdodDogNTAwcHg7CiAgb3ZlcmZsb3cteTogYXV0bzsKfQoKLnJ1bGVzLWNvbnRlbnQgaDMgewogIG1hcmdpbi10b3A6IDA7CiAgY29sb3I6ICMzMzM7Cn0KCi5ydWxlLXNlY3Rpb24gewogIG1hcmdpbi1ib3R0b206IDI1cHg7Cn0KCi5ydWxlLXNlY3Rpb24gaDQgewogIGNvbG9yOiAjNDA5ZWZmOwogIG1hcmdpbi1ib3R0b206IDEwcHg7Cn0KCi5ydWxlLXNlY3Rpb24gcCB7CiAgbWFyZ2luOiA4cHggMDsKICBsaW5lLWhlaWdodDogMS42OwogIGNvbG9yOiAjNjY2Owp9CgoucnVsZS1zZWN0aW9uIGNvZGUgewogIGJhY2tncm91bmQ6ICNmMWYyZjM7CiAgcGFkZGluZzogMnB4IDZweDsKICBib3JkZXItcmFkaXVzOiAzcHg7CiAgZm9udC1mYW1pbHk6ICdDb3VyaWVyIE5ldycsIG1vbm9zcGFjZTsKICBjb2xvcjogI2U3NGMzYzsKfQoKLnJ1bGUtc2VjdGlvbiB1bCB7CiAgbWFyZ2luOiAxMHB4IDA7CiAgcGFkZGluZy1sZWZ0OiAyMHB4Owp9CgoucnVsZS1zZWN0aW9uIGxpIHsKICBtYXJnaW46IDVweCAwOwogIGNvbG9yOiAjNjY2Owp9CgouZXhhbXBsZS1zZWN0aW9uIHsKICBtYXJnaW4tdG9wOiAzMHB4Owp9CgouZXhhbXBsZS1zZWN0aW9uIGg0IHsKICBjb2xvcjogIzY3YzIzYTsKICBtYXJnaW4tYm90dG9tOiAxNXB4Owp9CgouZXhhbXBsZS1jb2RlIHsKICBiYWNrZ3JvdW5kOiAjZjhmOWZhOwogIGJvcmRlcjogMXB4IHNvbGlkICNlNGU3ZWQ7CiAgYm9yZGVyLXJhZGl1czogNnB4OwogIHBhZGRpbmc6IDIwcHg7CiAgZm9udC1mYW1pbHk6ICdDb3VyaWVyIE5ldycsIG1vbm9zcGFjZTsKICBmb250LXNpemU6IDEzcHg7CiAgbGluZS1oZWlnaHQ6IDEuNjsKICBjb2xvcjogIzMzMzsKICB3aGl0ZS1zcGFjZTogcHJlLXdyYXA7CiAgb3ZlcmZsb3cteDogYXV0bzsKfQoKLyog5paw55qE6KeE6IyD5a+56K+d5qGG5qC35byPICovCi5ydWxlcy1kaWFsb2cgLnJ1bGVzLXRhYnMgewogIG1hcmdpbi10b3A6IC0yMHB4Owp9CgoucnVsZXMtZGlhbG9nIC5leGFtcGxlLWNvbnRlbnQgewogIG1heC1oZWlnaHQ6IDYwdmg7CiAgb3ZlcmZsb3cteTogYXV0bzsKICBwYWRkaW5nOiAwIDEwcHg7Cn0KCi5ydWxlcy1kaWFsb2cgLmV4YW1wbGUtaXRlbSB7CiAgbWFyZ2luLWJvdHRvbTogMjVweDsKICBwYWRkaW5nOiAxNXB4OwogIGJhY2tncm91bmQ6ICNmOGY5ZmE7CiAgYm9yZGVyLXJhZGl1czogNnB4OwogIGJvcmRlci1sZWZ0OiA0cHggc29saWQgIzQwOWVmZjsKfQoKLnJ1bGVzLWRpYWxvZyAuZXhhbXBsZS1pdGVtIHAgewogIG1hcmdpbjogNXB4IDA7CiAgbGluZS1oZWlnaHQ6IDEuNjsKICBjb2xvcjogIzMzMzsKfQoKLnJ1bGVzLWRpYWxvZyAuZXhhbXBsZS1pdGVtIHA6Zmlyc3QtY2hpbGQgewogIGZvbnQtd2VpZ2h0OiBib2xkOwogIGNvbG9yOiAjNDA5ZWZmOwogIG1hcmdpbi1ib3R0b206IDEwcHg7Cn0KCi5ydWxlcy1kaWFsb2cgLnJ1bGUtc2VjdGlvbiBwOmZpcnN0LWNoaWxkIHsKICBjb2xvcjogIzQwOWVmZjsKICBmb250LXdlaWdodDogYm9sZDsKICBtYXJnaW4tYm90dG9tOiAxMHB4Owp9CgovKiDpooTop4jlpLTpg6jmoLflvI8gKi8KLnByZXZpZXctaGVhZGVyIHsKICBkaXNwbGF5OiBmbGV4OwogIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIHBhZGRpbmc6IDE1cHggMjBweDsKICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U0ZTdlZDsKICBiYWNrZ3JvdW5kOiAjZjhmOWZhOwp9CgoucHJldmlldy1oZWFkZXIgaDQgewogIG1hcmdpbjogMDsKICBjb2xvcjogIzMwMzEzMzsKICBmb250LXNpemU6IDE2cHg7CiAgZm9udC13ZWlnaHQ6IDYwMDsKfQoKLnByZXZpZXctYWN0aW9ucyB7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwp9CgoudG9nZ2xlLWFsbC1idG4gewogIGNvbG9yOiAjNDA5ZWZmOwogIGZvbnQtc2l6ZTogMTNweDsKICBwYWRkaW5nOiA1cHggMTBweDsKfQoKLnRvZ2dsZS1hbGwtYnRuOmhvdmVyIHsKICBjb2xvcjogIzY2YjFmZjsKfQoKLnRvZ2dsZS1hbGwtYnRuIGkgewogIG1hcmdpbi1yaWdodDogNHB4Owp9CgovKiDlr7zlhaXpgInpobnmoLflvI8gKi8KLmltcG9ydC1vcHRpb25zIHsKICBkaXNwbGF5OiBmbGV4OwogIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47CiAgZ2FwOiAxMnB4OwogIG1hcmdpbi10b3A6IDE1cHg7CiAgcGFkZGluZzogMTVweDsKICBiYWNrZ3JvdW5kOiAjZjhmOWZhOwogIGJvcmRlci1yYWRpdXM6IDZweDsKICBib3JkZXI6IDFweCBzb2xpZCAjZTllY2VmOwp9CgouaW1wb3J0LW9wdGlvbnMgLmVsLWNoZWNrYm94IHsKICBtYXJnaW4tcmlnaHQ6IDA7CiAgbWFyZ2luLWJvdHRvbTogMDsKfQoKLmltcG9ydC1vcHRpb25zIC5lbC1jaGVja2JveF9fbGFiZWwgewogIGZvbnQtc2l6ZTogMTRweDsKICBjb2xvcjogIzMzMzsKICBmb250LXdlaWdodDogNTAwOwp9CgouaW1wb3J0LW9wdGlvbnMgLmVsLXRvb2x0aXAgewogIGN1cnNvcjogaGVscDsKfQoKLmltcG9ydC1wcm9ncmVzcyB7CiAgbWFyZ2luLXRvcDogMjBweDsKICBwYWRkaW5nOiAxNXB4OwogIGJhY2tncm91bmQ6ICNmZmY7CiAgYm9yZGVyLXJhZGl1czogNnB4OwogIGJvcmRlcjogMXB4IHNvbGlkICNlMWY1ZmU7Cn0KCi5pbXBvcnQtcHJvZ3Jlc3MgLmVsLXByb2dyZXNzIHsKICBtYXJnaW4tYm90dG9tOiAwOwp9CgouaW1wb3J0LXByb2dyZXNzIC5lbC1wcm9ncmVzc19fdGV4dCB7CiAgZm9udC1zaXplOiAxNHB4ICFpbXBvcnRhbnQ7CiAgZm9udC13ZWlnaHQ6IDUwMDsKICBjb2xvcjogIzQwOWVmZjsKfQoKLyog6aKY55uu5YWD5L+h5oGv5qC35byPICovCi5xdWVzdGlvbi1tZXRhIHsKICBtYXJnaW4tdG9wOiAxNXB4OwogIHBhZGRpbmctdG9wOiAxNXB4OwogIGJvcmRlci10b3A6IDFweCBzb2xpZCAjZjBmMGYwOwp9CgoucXVlc3Rpb24tYW5zd2VyLAoucXVlc3Rpb24tZXhwbGFuYXRpb24sCi5xdWVzdGlvbi1kaWZmaWN1bHR5IHsKICBtYXJnaW46IDZweCAwOwogIHBhZGRpbmc6IDZweCAxMHB4OwogIGJvcmRlci1yYWRpdXM6IDRweDsKICBmb250LXNpemU6IDEzcHg7CiAgbGluZS1oZWlnaHQ6IDEuNDsKfQoKLnF1ZXN0aW9uLWFuc3dlciB7CiAgYmFja2dyb3VuZDogI2U4ZjRmZDsKICBjb2xvcjogIzAwNjZjYzsKICBib3JkZXItbGVmdDogM3B4IHNvbGlkICM0MDllZmY7Cn0KCi5xdWVzdGlvbi1leHBsYW5hdGlvbiB7CiAgYmFja2dyb3VuZDogI2YwZjlmZjsKICBjb2xvcjogIzY2NjsKICBib3JkZXItbGVmdDogM3B4IHNvbGlkICM2N2MyM2E7Cn0KCi5xdWVzdGlvbi1kaWZmaWN1bHR5IHsKICBiYWNrZ3JvdW5kOiAjZmVmMGU2OwogIGNvbG9yOiAjZTZhMjNjOwogIGJvcmRlci1sZWZ0OiAzcHggc29saWQgI2U2YTIzYzsKfQoKLyog6aKE6KeI5rua5Yqo5Yy65Z+f5qC35byPICovCi5wcmV2aWV3LXNjcm9sbC13cmFwcGVyIHsKICBwYWRkaW5nLWJvdHRvbTogMzBweDsgLyog5Li65pyA5ZCO5LiA5Liq6aKY55uu5re75Yqg5bqV6YOo6L656LedICovCn0KCi8qIOmimOebrumhtumDqOagj+agt+W8jyAqLwoucXVlc3Rpb24tdG9wLWJhciB7CiAgZGlzcGxheTogZmxleDsKICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47CiAgYWxpZ24taXRlbXM6IGNlbnRlcjsKICBtYXJnaW4tYm90dG9tOiAxNXB4Owp9CgoucXVlc3Rpb24tdGl0bGUgewogIGZsZXg6IDE7Cn0KCi5xdWVzdGlvbi10b2dnbGUgewogIGZsZXgtc2hyaW5rOiAwOwp9CgoudG9nZ2xlLWJ0biB7CiAgY29sb3I6ICM5MDkzOTk7CiAgZm9udC1zaXplOiAxNnB4OwogIHBhZGRpbmc6IDRweDsKICBtaW4td2lkdGg6IGF1dG87Cn0KCi50b2dnbGUtYnRuOmhvdmVyIHsKICBjb2xvcjogIzQwOWVmZjsKfQoKLyog5a+85YWl5pON5L2c5Yy65Z+f5qC35byPICovCi5pbXBvcnQtYWN0aW9ucyB7CiAgZGlzcGxheTogZmxleDsKICBhbGlnbi1pdGVtczogY2VudGVyOwogIGdhcDogMTVweDsKICBwYWRkaW5nOiAxNXB4IDIwcHg7CiAgYmFja2dyb3VuZDogI2Y4ZjlmYTsKICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2U0ZTdlZDsKfQoKLyog5pyq5L+d5a2Y5pu05pS55oyH56S65ZmoICovCi51bnNhdmVkLWluZGljYXRvciB7CiAgY29sb3I6ICNmNTZjNmM7CiAgZm9udC1zaXplOiAxOHB4OwogIG1hcmdpbi1sZWZ0OiA4cHg7CiAgYW5pbWF0aW9uOiBibGluayAxLjVzIGluZmluaXRlOwp9CgpAa2V5ZnJhbWVzIGJsaW5rIHsKICAwJSwgNTAlIHsgb3BhY2l0eTogMTsgfQogIDUxJSwgMTAwJSB7IG9wYWNpdHk6IDAuMzsgfQp9Cg=="}, {"version": 3, "sources": ["detail.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA60EA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;;;AAIA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "detail.vue", "sourceRoot": "src/views/biz/questionBank", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <!-- 页面头部 -->\n    <div class=\"page-header\">\n      <!-- 标题行 -->\n      <div class=\"header-title\">\n        <el-button\n          type=\"primary\"\n          icon=\"el-icon-back\"\n          @click=\"goBack\"\n          style=\"margin-right: 15px;\"\n        >\n          返回题库列表\n        </el-button>\n        <h2 style=\"margin: 0; display: inline-block;\">{{ bankName }}</h2>\n      </div>\n\n      <!-- 搜索和统计行 -->\n      <div class=\"header-content\">\n        <!-- 搜索条件 -->\n        <div class=\"search-section\">\n          <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" label-width=\"68px\">\n            <el-form-item label=\"题型\" prop=\"questionType\">\n              <el-select v-model=\"queryParams.questionType\" placeholder=\"请选择题型\" clearable style=\"width: 120px;\">\n                <el-option label=\"单选题\" value=\"single\"></el-option>\n                <el-option label=\"多选题\" value=\"multiple\"></el-option>\n                <el-option label=\"判断题\" value=\"judgment\"></el-option>\n              </el-select>\n            </el-form-item>\n            <el-form-item label=\"难度\" prop=\"difficulty\">\n              <el-select v-model=\"queryParams.difficulty\" placeholder=\"请选择难度\" clearable style=\"width: 100px;\">\n                <el-option label=\"简单\" value=\"简单\"></el-option>\n                <el-option label=\"中等\" value=\"中等\"></el-option>\n                <el-option label=\"困难\" value=\"困难\"></el-option>\n              </el-select>\n            </el-form-item>\n            <el-form-item label=\"题目内容\" prop=\"questionContent\">\n              <el-input\n                v-model=\"queryParams.questionContent\"\n                placeholder=\"请输入题干内容关键词\"\n                clearable\n                style=\"width: 200px;\"\n                @keyup.enter.native=\"handleSearch\"\n              />\n            </el-form-item>\n            <el-form-item>\n              <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleSearch\">搜索</el-button>\n              <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetSearch\">重置</el-button>\n            </el-form-item>\n          </el-form>\n        </div>\n\n        <!-- 统计信息 -->\n        <div class=\"stats-section\">\n          <div class=\"stats-container\">\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">总题数</span>\n              <span class=\"stat-value\">{{ statistics.total }}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">单选题</span>\n              <span class=\"stat-value\">{{ statistics.singleChoice }}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">多选题</span>\n              <span class=\"stat-value\">{{ statistics.multipleChoice }}</span>\n            </div>\n            <div class=\"stat-item\">\n              <span class=\"stat-label\">判断题</span>\n              <span class=\"stat-value\">{{ statistics.judgment }}</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n\n\n\n    <!-- 操作栏 -->\n    <div class=\"operation-bar\">\n      <div class=\"operation-left\">\n        <el-button\n          type=\"success\"\n          icon=\"el-icon-upload2\"\n          @click=\"handleBatchImportClick\"\n        >\n          批量导题\n        </el-button>\n        <el-dropdown @command=\"handleAddQuestion\" style=\"margin-left: 10px;\">\n          <el-button type=\"primary\">\n            单个录入<i class=\"el-icon-arrow-down el-icon--right\"></i>\n          </el-button>\n          <el-dropdown-menu slot=\"dropdown\">\n            <el-dropdown-item command=\"single\">\n              <i class=\"el-icon-circle-check\" style=\"margin-right: 8px; color: #409eff;\"></i>\n              单选题\n            </el-dropdown-item>\n            <el-dropdown-item command=\"multiple\">\n              <i class=\"el-icon-finished\" style=\"margin-right: 8px; color: #67c23a;\"></i>\n              多选题\n            </el-dropdown-item>\n            <el-dropdown-item command=\"judgment\">\n              <i class=\"el-icon-success\" style=\"margin-right: 8px; color: #e6a23c;\"></i>\n              判断题\n            </el-dropdown-item>\n          </el-dropdown-menu>\n        </el-dropdown>\n\n        <!-- 操作按钮组 -->\n        <el-button-group style=\"margin-left: 10px;\">\n          <el-button\n            icon=\"el-icon-download\"\n            @click=\"handleExportQuestions\"\n          >\n            导出\n          </el-button>\n          <el-button\n            :type=\"isAllSelected ? 'primary' : 'default'\"\n            :icon=\"isAllSelected ? 'el-icon-check' : 'el-icon-minus'\"\n            @click=\"handleToggleSelectAll\"\n          >\n            {{ isAllSelected ? '全不选' : '全选' }}\n          </el-button>\n          <el-button\n            type=\"danger\"\n            icon=\"el-icon-delete\"\n            @click=\"handleBatchDelete\"\n            :disabled=\"selectedQuestions.length === 0\"\n          >\n            删除\n          </el-button>\n        </el-button-group>\n      </div>\n      <div class=\"operation-right\">\n        <el-button\n          :type=\"expandAll ? 'warning' : 'info'\"\n          :icon=\"expandAll ? 'el-icon-minus' : 'el-icon-plus'\"\n          @click=\"toggleExpandAll\"\n        >\n          {{ expandAll ? '收起所有题目' : '展开所有题目' }}\n        </el-button>\n      </div>\n    </div>\n\n\n\n    <!-- 题目列表 -->\n    <div class=\"question-list\">\n      <div v-if=\"questionList.length === 0\" class=\"empty-state\">\n        <el-empty description=\"暂无题目数据\">\n          <el-button type=\"primary\" @click=\"handleAddQuestion('single')\">添加第一道题目</el-button>\n        </el-empty>\n      </div>\n      <div v-else>\n        <question-card\n          v-for=\"(question, index) in questionList\"\n          :key=\"question.questionId\"\n          :question=\"question\"\n          :index=\"index + 1 + (queryParams.pageNum - 1) * queryParams.pageSize\"\n          :expanded=\"expandAll || expandedQuestions.includes(question.questionId)\"\n          :selected=\"selectedQuestions.includes(question.questionId)\"\n          @toggle-expand=\"handleToggleExpand\"\n          @edit=\"handleEditQuestion\"\n          @copy=\"handleCopyQuestion\"\n          @delete=\"handleDeleteQuestion\"\n          @selection-change=\"handleQuestionSelect\"\n        />\n      </div>\n    </div>\n\n    <!-- 分页 -->\n    <pagination\n      v-show=\"total > 0\"\n      :total=\"total\"\n      :page.sync=\"queryParams.pageNum\"\n      :limit.sync=\"queryParams.pageSize\"\n      @pagination=\"getQuestionList\"\n    />\n\n    <!-- 题目表单对话框 -->\n    <question-form\n      :visible.sync=\"questionFormVisible\"\n      :question-type=\"currentQuestionType\"\n      :question-data=\"currentQuestionData\"\n      :bank-id=\"bankId\"\n      @success=\"handleQuestionFormSuccess\"\n    />\n\n    <!-- 批量导入题目抽屉 -->\n    <el-drawer\n      title=\"批量导入题目\"\n      :visible.sync=\"importDrawerVisible\"\n      direction=\"rtl\"\n      size=\"90%\"\n      :show-close=\"true\"\n      :before-close=\"handleDrawerClose\"\n      class=\"batch-import-drawer\"\n    >\n      <div class=\"main el-row\">\n        <!-- 左侧编辑区域 -->\n        <div class=\"col-left h100p el-col el-col-12\">\n          <div class=\"toolbar clearfix\">\n            <div class=\"fr\">\n              <el-button\n                type=\"primary\"\n                size=\"mini\"\n                @click=\"showDocumentImportDialog\"\n              >\n                <i class=\"el-icon-folder-add\"></i>\n                文档导入\n              </el-button>\n              <el-button\n                type=\"primary\"\n                size=\"mini\"\n                @click=\"showRulesDialog\"\n              >\n                <i class=\"el-icon-reading\"></i>\n                输入规范与范例\n              </el-button>\n            </div>\n          </div>\n\n          <div class=\"editor-wrapper\">\n            <div id=\"rich-editor\" class=\"rich-editor-container\"></div>\n          </div>\n        </div>\n\n        <!-- 右侧解析结果区域 -->\n        <div class=\"col-right h100p el-col el-col-12\">\n          <div class=\"checkarea\">\n            <div class=\"import-actions\">\n              <el-button\n                type=\"success\"\n                size=\"mini\"\n                class=\"mr20\"\n                @click=\"confirmImport\"\n                :disabled=\"parsedQuestions.length === 0\"\n                :loading=\"importingQuestions\"\n              >\n                <i class=\"el-icon-upload2\"></i>\n                {{ importingQuestions ? '正在导入...' : '导入题目' }}\n              </el-button>\n\n              <div class=\"import-options\">\n                <el-checkbox\n                  v-model=\"importOptions.reverse\"\n                  :disabled=\"importingQuestions\"\n                >\n                  <el-tooltip content=\"勾选后将按题目顺序倒序导入，即最后一题先导入\" placement=\"top\">\n                    <span>按题目顺序倒序导入</span>\n                  </el-tooltip>\n                </el-checkbox>\n\n                <el-checkbox\n                  v-model=\"importOptions.allowDuplicate\"\n                  :disabled=\"importingQuestions\"\n                >\n                  <el-tooltip content=\"勾选后允许导入重复的题目内容，否则会跳过重复题目\" placement=\"top\">\n                    <span>允许题目重复</span>\n                  </el-tooltip>\n                </el-checkbox>\n              </div>\n\n              <div v-if=\"importingQuestions\" class=\"import-progress\">\n                <el-progress\n                  :percentage=\"importProgress\"\n                  :show-text=\"true\"\n                  :format=\"formatProgress\"\n                  status=\"success\"\n                  :stroke-width=\"6\"\n                />\n              </div>\n            </div>\n          </div>\n\n          <div class=\"preview-wrapper\">\n            <div class=\"preview-header\" v-if=\"parsedQuestions.length > 0\">\n              <h4>题目预览 ({{ parsedQuestions.length }})</h4>\n              <div class=\"preview-actions\">\n                <el-button\n                  type=\"text\"\n                  size=\"small\"\n                  @click=\"toggleAllQuestions\"\n                  class=\"toggle-all-btn\"\n                >\n                  <i :class=\"allExpanded ? 'el-icon-arrow-up' : 'el-icon-arrow-down'\"></i>\n                  {{ allExpanded ? '全部收起' : '全部展开' }}\n                </el-button>\n\n              </div>\n            </div>\n            <div class=\"preview-scroll-wrapper\">\n              <div v-if=\"parsedQuestions.length === 0\" class=\"empty-result\">\n                <i class=\"el-icon-document\"></i>\n                <p>暂无解析结果</p>\n                <p class=\"tip\">请在左侧输入题目内容</p>\n              </div>\n\n              <div\n                v-for=\"(question, index) in parsedQuestions\"\n                :key=\"index\"\n                class=\"el-card question-item is-hover-shadow\"\n              >\n                <div class=\"el-card__body\">\n                  <div class=\"question-top-bar\">\n                    <div class=\"question-title\">\n                      <font>{{ index + 1 }}. 【{{ getQuestionTypeName(question.questionType) }}】</font>\n                    </div>\n                    <div class=\"question-toggle\">\n                      <el-button\n                        type=\"text\"\n                        size=\"mini\"\n                        @click=\"toggleQuestion(index)\"\n                        class=\"toggle-btn\"\n                      >\n                        <i :class=\"question.collapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up'\"></i>\n                      </el-button>\n                    </div>\n                  </div>\n\n                  <!-- 题目内容始终显示 -->\n                  <div class=\"question-content\">\n                    <!-- 只显示题干 -->\n                    <div class=\"question-main-line\">\n                      <span class=\"display-latex rich-text\" v-html=\"getFormattedQuestionContent(question)\"></span>\n                    </div>\n\n                    <!-- 选项显示（如果有） -->\n                    <div v-if=\"question.options && question.options.length > 0\" class=\"question-options\">\n                      <div\n                        v-for=\"option in question.options\"\n                        :key=\"option.optionKey\"\n                        class=\"option-item\"\n                      >\n                        {{ option.optionKey }}. {{ option.optionContent }}\n                      </div>\n                    </div>\n                  </div>\n\n                  <!-- 答案、解析、难度可收起 -->\n                  <div v-show=\"!question.collapsed\" class=\"question-meta\">\n\n                    <div v-if=\"question.correctAnswer\" class=\"question-answer\">\n                      答案：{{ question.correctAnswer }}\n                    </div>\n\n                    <div v-if=\"question.difficulty && question.difficulty.trim() !== ''\" class=\"question-difficulty\">\n                      难度：{{ question.difficulty }}\n                    </div>\n\n                    <div v-if=\"question.explanation\" class=\"question-explanation\">\n                      解析：{{ question.explanation }}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </el-drawer>\n\n    <!-- 文档导入对话框 -->\n    <el-dialog\n      title=\"上传文档导入题目\"\n      :visible.sync=\"documentImportDialogVisible\"\n      width=\"700px\"\n      class=\"document-upload-dialog\"\n    >\n      <div style=\"text-align: center;\">\n        <div class=\"subtitle\" style=\"line-height: 3;\">\n          <i class=\"el-icon-info\"></i>\n          上传前请先下载模板，按照模板要求将内容录入到模板中。\n        </div>\n\n        <div style=\"padding: 14px;\">\n          <!-- <el-button\n            type=\"success\"\n            size=\"small\"\n            plain\n            @click=\"downloadExcelTemplate\"\n          >\n            <i class=\"el-icon-download\"></i>\n            下载excel模板\n          </el-button> -->\n\n          <el-button\n            type=\"success\"\n            size=\"small\"\n            plain\n            @click=\"downloadWordTemplate\"\n          >\n            <i class=\"el-icon-download\"></i>\n            下载Word模板\n          </el-button>\n        </div>\n\n        <div>\n          <el-upload\n            ref=\"documentUpload\"\n            class=\"upload-demo\"\n            drag\n            :action=\"uploadUrl\"\n            :headers=\"uploadHeaders\"\n            :data=\"uploadData\"\n            :on-success=\"handleUploadSuccess\"\n            :on-error=\"handleUploadError\"\n            :before-upload=\"beforeUpload\"\n            :accept=\"'.docx,.xlsx'\"\n            :limit=\"1\"\n            :disabled=\"isUploading || isParsing\"\n          >\n            <div v-if=\"!isUploading && !isParsing\">\n              <i class=\"el-icon-upload\"></i>\n              <div class=\"el-upload__text\">\n                将文件拖到此处，或<em>点击上传</em>\n              </div>\n            </div>\n            <div v-else-if=\"isUploading\" class=\"upload-loading\">\n              <i class=\"el-icon-loading\"></i>\n              <div class=\"el-upload__text\">正在上传文件...</div>\n            </div>\n            <div v-else-if=\"isParsing\" class=\"upload-loading\">\n              <i class=\"el-icon-loading\"></i>\n              <div class=\"el-upload__text\">正在解析文档，请稍候...</div>\n            </div>\n          </el-upload>\n        </div>\n\n        <div style=\"padding: 10px 20px; text-align: left; background-color: #f4f4f5; color: #909399; line-height: 1.4;\">\n          <div style=\"margin-bottom: 6px; font-weight: 700;\">说明</div>\n          1. 建议使用新版Office或WPS软件编辑题目文件，仅支持上传.docx格式的文件<br>\n          2. 题目数量过多、题目文件过大等情况建议分批导入<br>\n          3. 需严格按照各题型格式要求编辑题目文件\n        </div>\n      </div>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"documentImportDialogVisible = false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 输入规范与范例对话框 -->\n    <el-dialog\n      title=\"输入规范与范例\"\n      :visible.sync=\"rulesDialogVisible\"\n      width=\"900px\"\n      class=\"rules-dialog\"\n    >\n      <el-tabs v-model=\"activeRuleTab\" class=\"rules-tabs\">\n        <!-- 输入范例标签页 -->\n        <el-tab-pane label=\"输入范例\" name=\"examples\">\n          <div class=\"example-content\">\n            <div class=\"example-item\">\n              <p><strong>[单选题]</strong></p>\n              <p>1.（  ）是我国最早的诗歌总集，又称作\"诗三百\"。</p>\n              <p>A.《左传》</p>\n              <p>B.《离骚》</p>\n              <p>C.《坛经》</p>\n              <p>D.《诗经》</p>\n              <p>答案：D</p>\n              <p>解析：诗经是我国最早的诗歌总集。</p>\n              <p>难度：中等</p>\n            </div>\n\n            <div class=\"example-item\">\n              <p><strong>[多选题]</strong></p>\n              <p>2.中华人民共和国的成立，标志着（ ）。</p>\n              <p>A.中国新民主主义革命取得了基本胜利</p>\n              <p>B.中国现代史的开始</p>\n              <p>C.半殖民地半封建社会的结束</p>\n              <p>D.中国进入社会主义社会</p>\n              <p>答案：ABC</p>\n              <p>解析：新中国的成立，标志着我国新民主主义革命阶段的基本结束和社会主义革命阶段的开始。从中华人民共和国成立到社会主义改造基本完成，是我国从新民主主义到社会主义过渡的时期。这一时期，我国社会的性质是新民主主义社会。</p>\n            </div>\n\n            <div class=\"example-item\">\n              <p><strong>[判断题]</strong></p>\n              <p>3.元杂剧的四大悲剧是：关汉卿的《窦娥冤》，马致远的《汉宫秋》，白朴的《梧桐雨》和郑光祖的《赵氏孤儿》。</p>\n              <p>答案：错误</p>\n              <p>解析：元杂剧《赵氏孤儿》全名《冤报冤赵氏孤儿》，为纪君祥所作。《赵氏孤儿》非常典型地反映了中国悲剧那种前赴后继、不屈不饶地同邪恶势力斗争到底的抗争精神。</p>\n            </div>\n          </div>\n        </el-tab-pane>\n\n        <!-- 输入规范标签页 -->\n        <el-tab-pane label=\"输入规范\" name=\"rules\">\n          <div class=\"rules-content\">\n            <div class=\"rule-section\">\n              <p><strong>题号（必填）：</strong></p>\n              <p>1、题与题之间需要换行；</p>\n              <p>2、每题前面需要加上题号标识，题号后面需要加上符号（:：、.．）；</p>\n              <p>3、题号数字标识无需准确，只要有即可，系统自身会根据题目顺序排序；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>选项（必填）：</strong></p>\n              <p>1、题干和第一个选项之间需要换行；</p>\n              <p>2、选项与选项之间，可以换行，也可以在同一行；</p>\n              <p>3、如果选项在同一行，选项之间至少需要有一个空格；</p>\n              <p>4、选项格式（A:），字母可以为A到Z的任意大小写字母，冒号可以替换为\":：、.．\"其中之一；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>答案（必填）：</strong></p>\n              <p>1、答案支持直接在题干中标注，也可以显式标注在选项下面，优先以显式标注的答案为准；</p>\n              <p>2、显式标注格式（答案：），冒号可以替换为 \":：、\"其中之一；</p>\n              <p>3、题干中格式（【A】），括号可以替换为中英文的小括号或者中括号；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>解析（不必填）：</strong></p>\n              <p>1、解析格式（解析：），冒号可以替换为 \":：、\"其中之一；</p>\n            </div>\n\n            <div class=\"rule-section\">\n              <p><strong>难度（不必填）：</strong></p>\n              <p>1、难度格式（难度：），冒号可以替换为 \":：、\"其中之一；</p>\n              <p>2、难度级别只支持：简单、中等、困难 三个标准级别；</p>\n            </div>\n          </div>\n        </el-tab-pane>\n      </el-tabs>\n\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button @click=\"rulesDialogVisible = false\">关 闭</el-button>\n        <el-button type=\"primary\" @click=\"copyExampleToEditor\">将范例复制到编辑区</el-button>\n      </div>\n    </el-dialog>\n\n\n  </div>\n</template>\n\n<script>\nimport QuestionCard from './components/QuestionCard'\nimport QuestionForm from './components/QuestionForm'\nimport { listQuestion, delQuestion, getQuestionStatistics, batchImportQuestions, exportQuestionsToWord } from '@/api/biz/question'\n\nexport default {\n  name: \"QuestionBankDetail\",\n  components: {\n    QuestionCard,\n    QuestionForm\n  },\n  data() {\n    return {\n      // 题库信息\n      bankId: null,\n      bankName: '',\n      // 统计数据\n      statistics: {\n        total: 0,\n        singleChoice: 0,\n        multipleChoice: 0,\n        judgment: 0\n      },\n      // 题目列表\n      questionList: [],\n      // 分页参数\n      total: 0,\n      queryParams: {\n        pageNum: 1,\n        pageSize: 10,\n        bankId: null,\n        questionType: null,\n        difficulty: null,\n        questionContent: null\n      },\n      // 展开状态\n      expandAll: false,\n      expandedQuestions: [],\n      // 选择状态\n      selectedQuestions: [],\n      isAllSelected: false,\n      // 表单相关\n      questionFormVisible: false,\n      currentQuestionType: 'single',\n      currentQuestionData: null,\n      // 批量导入\n      importDrawerVisible: false,\n      // 文档导入抽屉\n      documentContent: '',\n      documentHtmlContent: '',\n      parsedQuestions: [],\n      parseErrors: [],\n      allExpanded: true,\n      isSettingFromBackend: false,\n      documentImportDialogVisible: false,\n      rulesDialogVisible: false,\n      activeRuleTab: 'examples',\n      // 上传和解析状态\n      isUploading: false,\n      isParsing: false,\n      importingQuestions: false,\n      importProgress: 0,\n      importOptions: {\n        reverse: false,\n        allowDuplicate: false\n      },\n      // 文件上传\n      uploadUrl: process.env.VUE_APP_BASE_API + '/biz/questionBank/uploadDocument',\n      uploadHeaders: {\n        Authorization: 'Bearer ' + this.$store.getters.token\n      },\n      uploadData: {},\n      // 富文本编辑器\n      richEditor: null,\n      editorInitialized: false\n    }\n  },\n\n  watch: {\n    // 监听文档内容变化，自动解析\n    documentContent: {\n      handler(newVal) {\n        // 如果是从后端设置内容，不触发前端解析\n        if (this.isSettingFromBackend) {\n          return\n        }\n\n        if (newVal && newVal.trim()) {\n          this.debounceParseDocument()\n        } else {\n          this.parsedQuestions = []\n          this.parseErrors = []\n        }\n      },\n      immediate: false\n    },\n    // 监听抽屉打开状态\n    importDrawerVisible: {\n      handler(newVal) {\n        if (newVal) {\n          // 抽屉打开时清空所有内容并初始化编辑器\n          this.clearImportContent()\n          this.$nextTick(() => {\n            this.initRichEditor()\n          })\n        } else {\n          // 抽屉关闭时销毁编辑器\n          if (this.richEditor) {\n            this.richEditor.destroy()\n            this.richEditor = null\n            this.editorInitialized = false\n          }\n        }\n      },\n      immediate: false\n    }\n  },\n\n  created() {\n    this.initPage()\n    // 创建防抖函数 - 增加延时到2秒，减少卡顿\n    this.debounceParseDocument = this.debounce(this.parseDocument, 2000)\n    // 创建编辑器内容变化的防抖函数 - 延时1.5秒\n    this.debounceEditorContentChange = this.debounce(this.handleEditorContentChangeDebounced, 1500)\n    // 初始化上传数据\n    this.uploadData = {\n      bankId: this.bankId\n    }\n    this.uploadHeaders = {\n      Authorization: 'Bearer ' + this.$store.getters.token\n    }\n  },\n\n  mounted() {\n    // 编辑器将在抽屉打开时初始化\n\n  },\n\n  beforeDestroy() {\n    // 取消所有防抖函数\n    if (this.debounceParseDocument && this.debounceParseDocument.cancel) {\n      this.debounceParseDocument.cancel()\n    }\n    if (this.debounceEditorContentChange && this.debounceEditorContentChange.cancel) {\n      this.debounceEditorContentChange.cancel()\n    }\n\n    // 销毁富文本编辑器\n    if (this.richEditor) {\n      this.richEditor.destroy()\n      this.richEditor = null\n    }\n  },\n  methods: {\n    // 初始化页面\n    initPage() {\n      const { bankId, bankName } = this.$route.query\n      if (!bankId) {\n        this.$message.error('缺少题库ID参数')\n        this.goBack()\n        return\n      }\n      this.bankId = bankId\n      this.bankName = bankName || '题库详情'\n      this.queryParams.bankId = bankId\n      this.getQuestionList()\n      this.getStatistics()\n    },\n    // 返回题库列表\n    goBack() {\n      this.$router.back()\n    },\n    // 获取题目列表\n    getQuestionList() {\n      // 转换查询参数格式\n      const params = this.convertQueryParams(this.queryParams)\n      listQuestion(params).then(response => {\n        this.questionList = response.rows\n        this.total = response.total\n      }).catch(() => {\n        this.$message.error('获取题目列表失败')\n      })\n    },\n\n    // 转换查询参数格式\n    convertQueryParams(params) {\n      const convertedParams = { ...params }\n\n      // 转换题型\n      if (convertedParams.questionType) {\n        const typeMap = {\n          'single': 1,\n          'multiple': 2,\n          'judgment': 3\n        }\n        convertedParams.questionType = typeMap[convertedParams.questionType] || convertedParams.questionType\n      }\n\n      // 转换难度\n      if (convertedParams.difficulty) {\n        const difficultyMap = {\n          '简单': 1,\n          '中等': 2,\n          '困难': 3\n        }\n        convertedParams.difficulty = difficultyMap[convertedParams.difficulty] || convertedParams.difficulty\n      }\n\n      // 清理空值\n      Object.keys(convertedParams).forEach(key => {\n        if (convertedParams[key] === '' || convertedParams[key] === null || convertedParams[key] === undefined) {\n          delete convertedParams[key]\n        }\n      })\n\n      return convertedParams\n    },\n    // 获取统计数据\n    getStatistics() {\n      getQuestionStatistics(this.bankId).then(response => {\n        this.statistics = response.data\n      }).catch(() => {\n        // 使用模拟数据\n        this.statistics = {\n          total: 0,\n          singleChoice: 0,\n          multipleChoice: 0,\n          judgment: 0\n        }\n      })\n    },\n\n\n    // 处理批量导题按钮点击\n    handleBatchImportClick() {\n      this.importDrawerVisible = true\n    },\n    // 添加题目\n    handleAddQuestion(type) {\n      this.currentQuestionType = type\n      this.currentQuestionData = null\n      this.questionFormVisible = true\n    },\n    // 切换展开状态\n    toggleExpandAll() {\n      this.expandAll = !this.expandAll\n      if (!this.expandAll) {\n        this.expandedQuestions = []\n      }\n    },\n\n\n\n    // 导出题目\n    handleExportQuestions() {\n      // 确认导出\n      this.$confirm(`确认导出题库\"${this.bankName}\"中的所有题目吗？`, '导出确认', {\n        confirmButtonText: '确定导出',\n        cancelButtonText: '取消',\n        type: 'info'\n      }).then(() => {\n        const loading = this.$loading({\n          lock: true,\n          text: `正在导出题库中的所有题目...`,\n          spinner: 'el-icon-loading',\n          background: 'rgba(0, 0, 0, 0.7)'\n        })\n\n        // 调用导出API - 导出当前题库的所有题目\n        exportQuestionsToWord({\n          bankId: this.bankId,\n          bankName: this.bankName\n        }).then(response => {\n          loading.close()\n\n          // 创建下载链接\n          const blob = new Blob([response], {\n            type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'\n          })\n          const url = window.URL.createObjectURL(blob)\n          const link = document.createElement('a')\n          link.href = url\n          link.download = `${this.bankName}.docx`\n          document.body.appendChild(link)\n          link.click()\n          document.body.removeChild(link)\n          window.URL.revokeObjectURL(url)\n\n          this.$message.success(`成功导出题库\"${this.bankName}\"`)\n        }).catch(error => {\n          loading.close()\n          console.error('导出失败:', error)\n          this.$message.error('导出失败，请重试')\n        })\n      }).catch(() => {\n        // 用户取消导出\n      })\n    },\n\n    // 切换全选/全不选\n    handleToggleSelectAll() {\n      this.isAllSelected = !this.isAllSelected\n      if (this.isAllSelected) {\n        // 全选\n        this.selectedQuestions = this.questionList.map(q => q.questionId)\n        this.$message.success(`已选择 ${this.selectedQuestions.length} 道题目`)\n      } else {\n        // 全不选\n        this.selectedQuestions = []\n        this.$message.success('已取消选择所有题目')\n      }\n    },\n\n\n\n    // 批量删除（优化版本）\n    handleBatchDelete() {\n      if (this.selectedQuestions.length === 0) {\n        this.$message.warning('请先选择要删除的题目')\n        return\n      }\n\n      const deleteCount = this.selectedQuestions.length\n      let confirmMessage = `确认删除选中的 ${deleteCount} 道题目吗？`\n\n      if (deleteCount > 20) {\n        confirmMessage += '\\n\\n注意：题目较多，删除可能需要一些时间，请耐心等待。'\n      }\n\n      this.$confirm(confirmMessage, '批量删除', {\n        confirmButtonText: '确定删除',\n        cancelButtonText: '取消',\n        type: 'warning',\n        dangerouslyUseHTMLString: false\n      }).then(() => {\n        this.performBatchDelete()\n      }).catch(() => {\n        this.$message.info('已取消删除')\n      })\n    },\n\n    // 执行批量删除\n    async performBatchDelete() {\n      const deleteCount = this.selectedQuestions.length\n      const loading = this.$loading({\n        lock: true,\n        text: `正在删除 ${deleteCount} 道题目，请稍候...`,\n        spinner: 'el-icon-loading',\n        background: 'rgba(0, 0, 0, 0.7)'\n      })\n\n      try {\n        // 使用真正的批量删除API\n        const questionIds = this.selectedQuestions.join(',')\n        const startTime = Date.now()\n\n        await delQuestion(questionIds) // 调用批量删除API\n\n        const endTime = Date.now()\n        const duration = ((endTime - startTime) / 1000).toFixed(1)\n\n        loading.close()\n        this.$message.success(`成功删除 ${deleteCount} 道题目 (耗时 ${duration}s)`)\n\n        // 清理选择状态\n        this.selectedQuestions = []\n        this.isAllSelected = false\n\n        // 刷新数据\n        this.getQuestionList()\n        this.getStatistics()\n\n      } catch (error) {\n        loading.close()\n        console.error('批量删除失败:', error)\n\n        let errorMessage = '批量删除失败'\n        if (error.response && error.response.data && error.response.data.msg) {\n          errorMessage = error.response.data.msg\n        } else if (error.message) {\n          errorMessage = error.message\n        }\n\n        this.$message.error(errorMessage)\n      }\n    },\n\n    // 题目选择状态变化\n    handleQuestionSelect(questionId, selected) {\n      if (selected) {\n        if (!this.selectedQuestions.includes(questionId)) {\n          this.selectedQuestions.push(questionId)\n        }\n      } else {\n        const index = this.selectedQuestions.indexOf(questionId)\n        if (index > -1) {\n          this.selectedQuestions.splice(index, 1)\n        }\n      }\n\n      // 更新全选状态\n      this.isAllSelected = this.selectedQuestions.length === this.questionList.length\n    },\n    // 切换单个题目展开状态\n    handleToggleExpand(questionId) {\n      const index = this.expandedQuestions.indexOf(questionId)\n      if (index > -1) {\n        // 收起题目\n        this.expandedQuestions.splice(index, 1)\n        // 如果当前是\"展开所有\"状态，则取消\"展开所有\"状态\n        if (this.expandAll) {\n          this.expandAll = false\n          // 将其他题目添加到expandedQuestions数组中，除了当前要收起的题目\n          this.questionList.forEach(question => {\n            if (question.questionId !== questionId && !this.expandedQuestions.includes(question.questionId)) {\n              this.expandedQuestions.push(question.questionId)\n            }\n          })\n        }\n      } else {\n        // 展开题目\n        this.expandedQuestions.push(questionId)\n      }\n    },\n    // 编辑题目\n    handleEditQuestion(question) {\n      this.currentQuestionData = question\n      this.currentQuestionType = question.questionType\n      this.questionFormVisible = true\n    },\n    // 复制题目\n    handleCopyQuestion(question) {\n      // 创建复制的题目数据（移除ID相关字段）\n      const copiedQuestion = {\n        ...question,\n        questionId: null,  // 清除ID，表示新增\n        createTime: null,\n        updateTime: null,\n        createBy: null,\n        updateBy: null\n      }\n\n      // 设置为编辑模式并打开表单\n      this.currentQuestionData = copiedQuestion\n      this.currentQuestionType = this.convertQuestionTypeToString(question.questionType)\n      this.questionFormVisible = true\n    },\n\n    // 题型数字转字符串（用于复制功能）\n    convertQuestionTypeToString(type) {\n      const typeMap = {\n        1: 'single',\n        2: 'multiple',\n        3: 'judgment'\n      }\n      return typeMap[type] || type\n    },\n    // 删除题目\n    handleDeleteQuestion(question) {\n      const questionContent = question.questionContent.replace(/<[^>]*>/g, '')\n      const displayContent = questionContent.length > 50 ? questionContent.substring(0, 50) + '...' : questionContent\n      this.$confirm(`确认删除题目\"${displayContent}\"吗？`, '提示', {\n        confirmButtonText: '确定',\n        cancelButtonText: '取消',\n        type: 'warning'\n      }).then(() => {\n        delQuestion(question.questionId).then(() => {\n          this.$message.success('删除成功')\n          this.getQuestionList()\n          this.getStatistics()\n        }).catch(() => {\n          this.$message.error('删除题目失败')\n        })\n      })\n    },\n    // 题目表单成功回调\n    handleQuestionFormSuccess() {\n      this.questionFormVisible = false\n      this.getQuestionList()\n      this.getStatistics()\n    },\n\n\n\n    // 抽屉关闭前处理\n    handleDrawerClose(done) {\n      // 检查是否有未保存的内容\n      const hasContent = this.documentContent && this.documentContent.trim().length > 0\n      const hasParsedQuestions = this.parsedQuestions && this.parsedQuestions.length > 0\n\n      if (hasContent || hasParsedQuestions) {\n        let message = '关闭后将丢失当前编辑的内容，确认关闭吗？'\n        if (hasParsedQuestions) {\n          message = `当前已解析出 ${this.parsedQuestions.length} 道题目，关闭后将丢失所有内容，确认关闭吗？`\n        }\n\n        this.$confirm(message, '确认关闭', {\n          confirmButtonText: '确定关闭',\n          cancelButtonText: '继续编辑',\n          type: 'warning'\n        }).then(() => {\n          // 清空内容\n          this.clearImportContent()\n          done()\n        }).catch(() => {\n          // 取消关闭，继续编辑\n        })\n      } else {\n        // 没有内容直接关闭\n        done()\n      }\n    },\n\n    // 清空导入内容\n    clearImportContent() {\n      // 清空文档内容\n      this.documentContent = ''\n      this.documentHtmlContent = ''\n\n      // 清空解析结果\n      this.parsedQuestions = []\n      this.parseErrors = []\n\n      // 重置解析状态\n      this.allExpanded = true\n      this.isSettingFromBackend = false\n\n      // 重置上传状态\n      this.isUploading = false\n      this.isParsing = false\n      this.importingQuestions = false\n      this.importProgress = 0\n\n      // 重置导入选项\n      this.importOptions = {\n        reverse: false,\n        allowDuplicate: false\n      }\n    },\n\n    // 显示文档导入对话框\n    showDocumentImportDialog() {\n      // 清除上一次的上传状态和内容\n      this.isUploading = false\n      this.isParsing = false\n\n      // 清除上传组件的文件列表\n      this.$nextTick(() => {\n        const uploadComponent = this.$refs.documentUpload\n        if (uploadComponent) {\n          uploadComponent.clearFiles()\n        }\n      })\n\n      this.documentImportDialogVisible = true\n\n    },\n\n    // 显示规范对话框\n    showRulesDialog() {\n      this.activeRuleTab = 'examples' // 默认显示范例标签页\n      this.rulesDialogVisible = true\n    },\n\n    // 将范例复制到编辑区 - 只保留前3题：单选、多选、判断\n    copyExampleToEditor() {\n      // 使用输入范例标签页里的前3题内容，转换为HTML格式\n      const htmlTemplate = `\n<p>1.（  ）是我国最早的诗歌总集，又称作\"诗三百\"。</p>\n<p>A.《左传》</p>\n<p>B.《离骚》</p>\n<p>C.《坛经》</p>\n<p>D.《诗经》</p>\n<p>答案：D</p>\n<p>解析：诗经是我国最早的诗歌总集。</p>\n<p>难度：中等</p>\n<p><br></p>\n\n<p>2.中华人民共和国的成立，标志着（ ）。</p>\n<p>A.中国新民主主义革命取得了基本胜利</p>\n<p>B.中国现代史的开始</p>\n<p>C.半殖民地半封建社会的结束</p>\n<p>D.中国进入社会主义社会</p>\n<p>答案：ABC</p>\n<p>解析：新中国的成立，标志着我国新民主主义革命阶段的基本结束和社会主义革命阶段的开始。</p>\n<p><br></p>\n\n<p>3.元杂剧的四大悲剧是：关汉卿的《窦娥冤》，马致远的《汉宫秋》，白朴的《梧桐雨》和郑光祖的《赵氏孤儿》。</p>\n<p>答案：错误</p>\n<p>解析：元杂剧《赵氏孤儿》全名《冤报冤赵氏孤儿》，为纪君祥所作。</p>\n      `.trim()\n\n      // 直接设置到富文本编辑器\n      if (this.richEditor && this.editorInitialized) {\n        this.richEditor.setData(htmlTemplate)\n\n      } else {\n        // 如果编辑器未初始化，等待初始化后再设置\n        this.$nextTick(() => {\n          if (this.richEditor && this.editorInitialized) {\n            this.richEditor.setData(htmlTemplate)\n\n          }\n        })\n      }\n\n      // 关闭对话框\n      this.rulesDialogVisible = false\n\n      // 提示用户\n      this.$message.success('输入范例已填充到编辑区，右侧将自动解析')\n\n\n    },\n\n\n\n    // 下载Word模板\n    downloadWordTemplate() {\n      this.download('biz/questionBank/downloadWordTemplate', {}, `题目导入Word模板.docx`)\n    },\n\n    // 上传前检查\n    beforeUpload(file) {\n\n\n      const isValidType = file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document' ||\n                         file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||\n                         file.name.endsWith('.docx') || file.name.endsWith('.xlsx')\n      const isLt10M = file.size / 1024 / 1024 < 10\n\n      if (!isValidType) {\n        this.$message.error('只能上传 .docx 或 .xlsx 格式的文件!')\n        return false\n      }\n      if (!isLt10M) {\n        this.$message.error('上传文件大小不能超过 10MB!')\n        return false\n      }\n\n      // 更新上传数据\n      this.uploadData.bankId = this.bankId\n\n      // 设置上传状态\n      this.isUploading = true\n      this.isParsing = false\n\n\n\n      return true\n    },\n\n    // 上传成功\n    handleUploadSuccess(response) {\n      if (response.code === 200) {\n        // 上传完成，开始解析\n        this.isUploading = false\n        this.isParsing = true\n\n\n\n        // 清除之前的解析结果，确保干净的开始\n        this.parsedQuestions = []\n        this.parseErrors = []\n\n        // 延迟关闭对话框，让用户看到解析动画\n        setTimeout(() => {\n          this.documentImportDialogVisible = false\n          this.isParsing = false\n        }, 1500)\n\n        // 设置标志位，避免触发前端重新解析\n        this.isSettingFromBackend = true\n\n        // 将解析结果显示在右侧\n        if (response.questions && response.questions.length > 0) {\n          this.parsedQuestions = response.questions.map(question => ({\n            ...question,\n            collapsed: false  // 默认展开\n          }))\n          // 重置全部展开状态\n          this.allExpanded = true\n          this.parseErrors = response.errors || []\n\n          // 显示详细的解析结果\n          const errorCount = response.errors ? response.errors.length : 0\n          if (errorCount > 0) {\n            this.$message.success(`成功解析出 ${response.questions.length} 道题目，有 ${errorCount} 个错误或警告`)\n          } else {\n            this.$message.success(`成功解析出 ${response.questions.length} 道题目`)\n          }\n\n\n        } else {\n          this.$message.error('未解析出任何题目，请检查文件格式')\n          this.parsedQuestions = []\n          this.parseErrors = response.errors || ['未能解析出题目内容']\n\n\n        }\n\n        // 将原始内容填充到富文本编辑器中\n        if (response.originalContent) {\n          this.setEditorContent(response.originalContent)\n          this.documentContent = response.originalContent\n          this.documentHtmlContent = response.originalContent // 初始化HTML内容\n\n        }\n\n        // 延迟重置标志位，确保所有异步操作完成\n        setTimeout(() => {\n          this.isSettingFromBackend = false\n        }, 2000)\n      } else {\n\n        this.$message.error(response.msg || '文件上传失败')\n        // 重置状态\n        this.isUploading = false\n        this.isParsing = false\n      }\n    },\n\n    // 上传失败\n    handleUploadError() {\n      this.$message.error('文件上传失败，请检查网络连接或联系管理员')\n\n      // 重置状态\n      this.isUploading = false\n      this.isParsing = false\n    },\n\n\n\n    // 切换题目展开/收起\n    toggleQuestion(index) {\n      const question = this.parsedQuestions[index]\n      this.$set(question, 'collapsed', !question.collapsed)\n    },\n\n    // 全部展开/收起\n    toggleAllQuestions() {\n      this.allExpanded = !this.allExpanded\n      this.parsedQuestions.forEach(question => {\n        this.$set(question, 'collapsed', !this.allExpanded)\n      })\n\n    },\n\n    // 确认导入\n    confirmImport() {\n      if (this.parsedQuestions.length === 0) {\n        this.$message.warning('没有可导入的题目')\n        return\n      }\n\n      // 构建确认信息\n      let confirmMessage = `确认导入 ${this.parsedQuestions.length} 道题目吗？`\n      let optionMessages = []\n\n      if (this.importOptions.reverse) {\n        optionMessages.push('将按倒序导入')\n      }\n      if (this.importOptions.allowDuplicate) {\n        optionMessages.push('允许重复题目')\n      }\n\n      if (optionMessages.length > 0) {\n        confirmMessage += `\\n\\n导入选项：${optionMessages.join('，')}`\n      }\n\n      this.$confirm(confirmMessage, '确认导入', {\n        confirmButtonText: '确定导入',\n        cancelButtonText: '取消',\n        type: 'info',\n        dangerouslyUseHTMLString: false\n      }).then(() => {\n        this.importQuestions()\n      }).catch(() => {})\n    },\n\n    // 导入题目\n    async importQuestions() {\n      this.importingQuestions = true\n      this.importProgress = 0\n\n      try {\n        // 处理导入选项\n        let questionsToImport = [...this.parsedQuestions]\n\n        if (this.importOptions.reverse) {\n          questionsToImport.reverse()\n          this.$message.info('已按倒序排列题目')\n        }\n\n        // 模拟进度更新\n        this.importProgress = 10\n\n        // 调用实际的导入API\n        const importData = {\n          bankId: this.bankId,\n          questions: questionsToImport,\n          allowDuplicate: this.importOptions.allowDuplicate,\n          reverse: this.importOptions.reverse\n        }\n\n        this.importProgress = 30\n\n        const response = await batchImportQuestions(importData)\n\n        this.importProgress = 80\n\n        if (response.code === 200) {\n          this.importProgress = 100\n\n          // 显示详细的导入结果\n          const result = response.data || {}\n          const successCount = result.successCount || 0\n          const failCount = result.failCount || 0\n          const skippedCount = result.skippedCount || 0\n\n          // 构建结果消息\n          let resultMessage = `导入完成：成功 ${successCount} 道`\n\n          if (failCount > 0) {\n            resultMessage += `，失败 ${failCount} 道`\n          }\n\n          if (skippedCount > 0) {\n            resultMessage += `，跳过重复 ${skippedCount} 道`\n          }\n\n          resultMessage += ' 题目'\n\n          // 根据结果类型显示不同的消息\n          if (failCount > 0 || skippedCount > 0) {\n            this.$message.warning(resultMessage)\n          } else {\n            this.$message.success(resultMessage)\n          }\n\n          // 如果有错误信息，显示详情\n          if (result.errors && result.errors.length > 0) {\n            console.warn('导入详情:', result.errors)\n\n            // 如果有跳过的题目，可以显示更详细的信息\n            if (skippedCount > 0) {\n              const skippedErrors = result.errors.filter(error => error.includes('重复跳过'))\n              if (skippedErrors.length > 0) {\n                console.info('跳过的重复题目:', skippedErrors)\n              }\n            }\n          }\n        } else {\n          throw new Error(response.msg || '导入失败')\n        }\n\n        // 清理状态并关闭抽屉\n        this.importDrawerVisible = false\n        this.documentContent = ''\n        this.documentHtmlContent = ''\n        this.parsedQuestions = []\n        this.parseErrors = []\n\n        // 刷新数据\n        this.getQuestionList()\n        this.getStatistics()\n\n      } catch (error) {\n        console.error('导入题目失败:', error)\n        this.$message.error('导入失败: ' + (error.message || '未知错误'))\n      } finally {\n        this.importingQuestions = false\n        this.importProgress = 0\n      }\n    },\n\n    // 格式化进度显示\n    formatProgress(percentage) {\n      if (percentage === 100) {\n        return '导入完成'\n      } else if (percentage >= 80) {\n        return '正在保存...'\n      } else if (percentage >= 30) {\n        return '正在处理...'\n      } else {\n        return '准备中...'\n      }\n    },\n\n    // 初始化富文本编辑器\n    initRichEditor() {\n      if (this.editorInitialized) {\n        return\n      }\n\n      // 检查CKEditor是否可用\n      if (!window.CKEDITOR) {\n        this.fallbackToTextarea()\n        return\n      }\n\n      try {\n        // 如果编辑器已存在，先销毁\n        if (this.richEditor) {\n          this.richEditor.destroy()\n          this.richEditor = null\n        }\n\n        // 确保容器存在\n        const editorContainer = document.getElementById('rich-editor')\n        if (!editorContainer) {\n          return\n        }\n\n        // 创建textarea元素\n        editorContainer.innerHTML = '<textarea id=\"rich-editor-textarea\" name=\"rich-editor-textarea\"></textarea>'\n\n        // 等待DOM更新后创建编辑器\n        this.$nextTick(() => {\n          // 检查CKEditor是否可用\n          if (!window.CKEDITOR || !window.CKEDITOR.replace) {\n\n            this.showFallbackEditor = true\n            return\n          }\n\n          try {\n            // 先尝试完整配置\n            this.richEditor = window.CKEDITOR.replace('rich-editor-textarea', {\n              height: 'calc(100vh - 200px)', // 全屏高度减去头部和其他元素的高度\n              toolbar: [\n                { name: 'styles', items: ['FontSize'] },\n                { name: 'basicstyles', items: ['Bold', 'Italic', 'Underline', 'Strike', 'Superscript', 'Subscript', '-', 'RemoveFormat'] },\n                { name: 'clipboard', items: ['Cut', 'Copy', 'Paste', 'PasteText'] },\n                { name: 'colors', items: ['TextColor', 'BGColor'] },\n                { name: 'paragraph', items: ['JustifyLeft', 'JustifyCenter', 'JustifyRight', 'JustifyBlock'] },\n                { name: 'editing', items: ['Undo', 'Redo'] },\n                { name: 'links', items: ['Link', 'Unlink'] },\n                { name: 'insert', items: ['Image', 'SpecialChar'] },\n                { name: 'tools', items: ['Maximize'] }\n              ],\n              removeButtons: '',\n              language: 'zh-cn',\n              removePlugins: 'elementspath',\n              resize_enabled: false,\n              extraPlugins: 'font,colorbutton,justify,specialchar,image',\n              allowedContent: true,\n              // 字体大小配置\n              fontSize_sizes: '12/12px;14/14px;16/16px;18/18px;20/20px;22/22px;24/24px;26/26px;28/28px;36/36px;48/48px;72/72px',\n              fontSize_defaultLabel: '14px',\n              // 颜色配置\n              colorButton_enableMore: true,\n              colorButton_colors: 'CF5D4E,454545,FFF,CCC,DDD,CCEAEE,66AB16',\n              // 图像上传配置 - 参考您提供的标准配置\n              filebrowserUploadUrl: process.env.VUE_APP_BASE_API + '/common/uploadImage',\n              image_previewText: ' ',\n              // 设置基础路径，让相对路径能正确解析到后端服务器\n              baseHref: 'http://localhost:8802/',\n              // 图像插入配置\n              image_previewText: '预览区域',\n              image_removeLinkByEmptyURL: true,\n              // 隐藏不需要的标签页，只保留上传和图像信息\n              removeDialogTabs: 'image:Link;image:advanced',\n              on: {\n                instanceReady: function(evt) {\n                  const editor = evt.editor\n                  editor.on('dialogShow', function(evt) {\n                    const dialog = evt.data\n                    if (dialog.getName() === 'image') {\n                      setTimeout(() => {\n                        const checkInterval = setInterval(() => {\n                          try {\n                            const urlField = dialog.getContentElement('info', 'txtUrl')\n                            if (urlField && urlField.getValue() && urlField.getValue().startsWith('/')) {\n                              clearInterval(checkInterval)\n                              dialog.selectPage('info')\n                            }\n                          } catch (e) {\n                            // 忽略错误\n                          }\n                        }, 500)\n                        setTimeout(() => clearInterval(checkInterval), 10000)\n                      }, 1000)\n                    }\n                  })\n                }\n              }\n            })\n          } catch (error) {\n            this.fallbackToTextarea()\n            return\n          }\n\n          // 监听内容变化 - 使用防抖优化性能\n          if (this.richEditor && this.richEditor.on) {\n            this.richEditor.on('change', () => {\n              this.debounceEditorContentChange()\n            })\n\n            this.richEditor.on('key', () => {\n              this.debounceEditorContentChange()\n            })\n\n            this.richEditor.on('instanceReady', () => {\n              this.editorInitialized = true\n              this.richEditor.setData('')\n            })\n          }\n        })\n\n      } catch (error) {\n        this.fallbackToTextarea()\n      }\n    },\n\n    // 处理编辑器内容变化（防抖后执行）\n    handleEditorContentChangeDebounced() {\n      if (!this.richEditor || !this.editorInitialized) {\n        return\n      }\n\n      try {\n        const rawContent = this.richEditor.getData()\n        const contentWithRelativeUrls = this.convertUrlsToRelative(rawContent)\n        this.documentHtmlContent = this.preserveRichTextFormatting(contentWithRelativeUrls)\n        this.documentContent = this.stripHtmlTagsKeepImages(contentWithRelativeUrls)\n      } catch (error) {\n        console.warn('编辑器内容处理失败:', error)\n      }\n    },\n\n    // 回退到普通文本框\n    fallbackToTextarea() {\n      const editorContainer = document.getElementById('rich-editor')\n      if (editorContainer) {\n        const textarea = document.createElement('textarea')\n        textarea.className = 'fallback-textarea'\n        textarea.placeholder = '请在此处粘贴或输入题目内容...'\n        textarea.value = '' // 确保文本框为空\n        textarea.style.cssText = 'width: 100%; height: 400px; border: 1px solid #ddd; padding: 10px; font-family: \"Courier New\", monospace; font-size: 14px; line-height: 1.6; resize: none;'\n\n        // 监听内容变化 - 使用防抖优化性能\n        textarea.addEventListener('input', (e) => {\n          // 立即更新内容，但防抖解析\n          this.documentContent = e.target.value\n          this.documentHtmlContent = e.target.value\n        })\n\n        editorContainer.innerHTML = ''\n        editorContainer.appendChild(textarea)\n        this.editorInitialized = true\n      }\n    },\n\n\n\n    // 设置编辑器内容\n    setEditorContent(content) {\n      if (this.richEditor && this.editorInitialized) {\n        this.richEditor.setData(content)\n      } else {\n        this.documentContent = content\n        this.documentHtmlContent = content\n      }\n    },\n\n\n\n    // 防抖函数 - 优化版本，支持取消\n    debounce(func, wait) {\n      let timeout\n      const debounced = function executedFunction(...args) {\n        const later = () => {\n          clearTimeout(timeout)\n          timeout = null\n          func.apply(this, args)\n        }\n        clearTimeout(timeout)\n        timeout = setTimeout(later, wait)\n      }\n\n      // 添加取消方法\n      debounced.cancel = function() {\n        clearTimeout(timeout)\n        timeout = null\n      }\n\n      return debounced\n    },\n\n    // 将编辑器内容中的完整URL转换为相对路径\n    convertUrlsToRelative(content) {\n      if (!content) return content\n\n      // 匹配当前域名的完整URL并转换为相对路径\n      const currentOrigin = window.location.origin\n      const urlRegex = new RegExp(currentOrigin.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&') + '(/[^\"\\'\\\\s>]*)', 'g')\n\n      return content.replace(urlRegex, '$1')\n    },\n\n    // 解析文档\n    parseDocument() {\n      if (!this.documentContent.trim()) {\n        this.parsedQuestions = []\n        this.parseErrors = []\n        return\n      }\n\n      try {\n        const parseResult = this.parseQuestionContent(this.documentContent)\n        this.parsedQuestions = parseResult.questions.map(question => ({\n          ...question,\n          collapsed: false\n        }))\n        this.parseErrors = parseResult.errors\n      } catch (error) {\n        this.parseErrors = ['解析失败：' + error.message]\n        this.parsedQuestions = []\n      }\n    },\n\n    // 解析题目内容 - 优化版本，更加健壮\n    parseQuestionContent(content) {\n      const questions = []\n      const errors = []\n\n      if (!content || typeof content !== 'string') {\n\n        return { questions, errors: ['解析内容为空或格式不正确'] }\n      }\n\n      try {\n\n\n        const textContent = this.stripHtmlTagsKeepImages(content)\n\n        if (!textContent || textContent.trim().length === 0) {\n          return { questions, errors: ['处理后的内容为空'] }\n        }\n\n        const lines = textContent.split('\\n').map(line => line.trim()).filter(line => line.length > 0)\n\n        if (lines.length === 0) {\n          return { questions, errors: ['没有有效的内容行'] }\n        }\n\n\n\n        let currentQuestionLines = []\n        let questionNumber = 0\n\n        for (let i = 0; i < lines.length; i++) {\n          const line = lines[i]\n\n          // 检查是否是题目开始行：数字、[题目类型] 或 [题目类型]\n          const isQuestionStart = this.isQuestionStartLine(line) || this.isQuestionTypeStart(line)\n\n          if (isQuestionStart) {\n            // 如果之前有题目内容，先处理之前的题目\n            if (currentQuestionLines.length > 0) {\n              try {\n                const questionText = currentQuestionLines.join('\\n')\n                const parsedQuestion = this.parseQuestionFromLines(questionText, questionNumber)\n                if (parsedQuestion) {\n                  questions.push(parsedQuestion)\n                }\n              } catch (error) {\n                errors.push(`第 ${questionNumber} 题解析失败: ${error.message}`)\n              }\n            }\n\n            // 开始新题目\n            currentQuestionLines = [line]\n            questionNumber++\n          } else {\n            // 如果当前在处理题目中，添加到当前题目\n            if (currentQuestionLines.length > 0) {\n              currentQuestionLines.push(line)\n            }\n          }\n        }\n\n        // 处理最后一个题目\n        if (currentQuestionLines.length > 0) {\n          try {\n            const questionText = currentQuestionLines.join('\\n')\n            const parsedQuestion = this.parseQuestionFromLines(questionText, questionNumber)\n            if (parsedQuestion) {\n              questions.push(parsedQuestion)\n            }\n          } catch (error) {\n            errors.push(`第 ${questionNumber} 题解析失败: ${error.message}`)\n          }\n        }\n\n      } catch (error) {\n        errors.push(`文档解析失败: ${error.message}`)\n      }\n\n      return { questions, errors }\n    },\n\n    // 判断是否为题目开始行 - 按照输入规范\n    isQuestionStartLine(line) {\n      // 规范：每题前面需要加上题号标识，题号后面需要加上符号（:：、.．）\n      // 匹配格式：数字 + 符号(:：、.．) + 可选空格\n      // 例如：1. 1、 1： 1． 等\n      return /^\\d+[.:：．、]\\s*/.test(line)\n    },\n\n    // 判断是否为题型标注开始行\n    isQuestionTypeStart(line) {\n      // 匹配格式：[题目类型]\n      // 例如：[单选题] [多选题] [判断题] 等\n      return /^\\[.*?题\\]/.test(line)\n    },\n\n    // 从行数组解析单个题目 - 按照输入规范\n    parseQuestionFromLines(questionText) {\n      const lines = questionText.split('\\n').map(line => line.trim()).filter(line => line.length > 0)\n\n      if (lines.length === 0) {\n        throw new Error('题目内容为空')\n      }\n\n      let questionType = 'judgment' // 默认判断题\n      let questionContent = ''\n      let contentStartIndex = 0\n\n      // 检查是否有题型标注（如 [单选题]、[多选题]、[判断题]）\n      for (let i = 0; i < lines.length; i++) {\n        const line = lines[i]\n        const typeMatch = line.match(/\\[(.*?题)\\]/)\n        if (typeMatch) {\n          const typeText = typeMatch[1]\n\n          // 转换题目类型\n          if (typeText.includes('判断')) {\n            questionType = 'judgment'\n          } else if (typeText.includes('单选')) {\n            questionType = 'single'\n          } else if (typeText.includes('多选')) {\n            questionType = 'multiple'\n          } else if (typeText.includes('填空')) {\n            questionType = 'fill'\n          } else if (typeText.includes('简答')) {\n            questionType = 'essay'\n          }\n\n          // 如果题型标注和题目内容在同一行\n          const remainingContent = line.replace(/\\[.*?题\\]/, '').trim()\n          if (remainingContent) {\n            questionContent = remainingContent\n            contentStartIndex = i + 1\n          } else {\n            contentStartIndex = i + 1\n          }\n          break\n        }\n      }\n\n      // 如果没有找到题型标注，从第一行开始解析\n      if (contentStartIndex === 0) {\n        contentStartIndex = 0\n      }\n\n      // 提取题目内容（从题号行开始）\n      for (let i = contentStartIndex; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 如果是题号行，提取题目内容（移除题号）\n        if (this.isQuestionStartLine(line)) {\n          // 移除题号，提取题目内容\n          questionContent = line.replace(/^\\d+[.:：．、]\\s*/, '').trim()\n          contentStartIndex = i + 1\n          break\n        } else if (!questionContent) {\n          // 如果还没有题目内容，当前行就是题目内容\n          questionContent = line\n          contentStartIndex = i + 1\n          break\n        }\n      }\n\n      // 继续收集题目内容（直到遇到选项或答案）\n      for (let i = contentStartIndex; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 如果遇到选项行、答案行、解析行或难度行，停止收集题目内容\n        if (this.isOptionLine(line) || this.isAnswerLine(line) ||\n            this.isExplanationLine(line) || this.isDifficultyLine(line)) {\n          break\n        }\n\n        // 继续添加到题目内容，但要确保不包含题号\n        let cleanLine = line\n        // 如果这行还包含题号，移除它\n        if (this.isQuestionStartLine(line)) {\n          cleanLine = line.replace(/^\\d+[.:：．、]\\s*/, '').trim()\n        }\n\n        if (cleanLine) {\n          if (questionContent) {\n            questionContent += '\\n' + cleanLine\n          } else {\n            questionContent = cleanLine\n          }\n        }\n      }\n\n      if (!questionContent) {\n        throw new Error('无法提取题目内容')\n      }\n\n      // 最终清理：确保题目内容不包含题号\n      let finalQuestionContent = questionContent.trim()\n      // 使用更强的清理逻辑，多次清理确保彻底移除题号\n      while (/^\\s*\\d+[.:：．、]/.test(finalQuestionContent)) {\n        finalQuestionContent = finalQuestionContent.replace(/^\\s*\\d+[.:：．、]\\s*/, '').trim()\n      }\n\n      // 额外清理：移除可能的HTML标签内的题号\n      if (finalQuestionContent.includes('<')) {\n        finalQuestionContent = this.removeQuestionNumber(finalQuestionContent)\n      }\n\n      const question = {\n        questionType: questionType,\n        type: questionType,\n        typeName: this.getTypeDisplayName(questionType),\n        questionContent: finalQuestionContent,\n        content: finalQuestionContent,\n        difficulty: '', // 不设置默认值\n        explanation: '',\n        options: [],\n        correctAnswer: '',\n        collapsed: false  // 默认展开\n      }\n\n      // 解析选项（对于选择题）\n      const optionResult = this.parseOptionsFromLines(lines, 0)\n      question.options = optionResult.options\n\n      // 根据选项数量推断题目类型（如果之前没有明确标注）\n      if (questionType === 'judgment' && question.options.length > 0) {\n        // 如果有选项，推断为选择题\n        questionType = 'single'  // 默认为单选题\n        question.questionType = questionType\n        question.type = questionType\n        question.typeName = this.getTypeDisplayName(questionType)\n      }\n\n      // 解析答案、解析、难度\n      this.parseQuestionMetaFromLines(lines, question)\n\n      // 根据答案长度进一步推断选择题类型\n      if (questionType === 'single' && question.correctAnswer && question.correctAnswer.length > 1) {\n        // 如果答案包含多个字母，推断为多选题\n        if (/^[A-Z]{2,}$/.test(question.correctAnswer)) {\n          questionType = 'multiple'\n          question.questionType = questionType\n          question.type = questionType\n          question.typeName = this.getTypeDisplayName(questionType)\n        }\n      }\n\n      // 最终清理：确保题目内容完全没有题号和题型标识\n      question.questionContent = this.removeQuestionNumber(question.questionContent)\n      question.questionContent = this.removeQuestionType(question.questionContent)\n      question.content = question.questionContent\n\n      return question\n    },\n\n    // 判断是否为选项行 - 按照输入规范\n    isOptionLine(line) {\n      // 规范：选项格式（A:），字母可以为A到Z的任意大小写字母，冒号可以替换为\":：、.．\"其中之一\n      // 严格验证：避免误将题目内容中的字母+符号识别为选项\n      if (!line || line.length > 200) {\n        return false\n      }\n\n      const match = line.match(/^([A-Za-z])[.:：．、]\\s*(.*)/)\n      if (match) {\n        const optionKey = match[1].toUpperCase()\n        const optionContent = match[2] ? match[2].trim() : ''\n\n        // 严格验证条件：\n        // 1. 选项字母必须是A-Z单个字母\n        // 2. 选项内容长度合理（1-100字符）\n        // 3. 排除明显的题目内容描述（如包含\"表示\"、\"数据\"等词汇的长句）\n        if (/^[A-Z]$/.test(optionKey) && optionContent.length > 0 && optionContent.length <= 100) {\n          // 排除明显的题目内容描述\n          const excludePatterns = [\n            /表示.*?数据/,     // 排除\"表示...数据\"这类描述\n            /一般用.*?或/,      // 排除\"一般用...或\"这类描述\n            /通常.*?来/,       // 排除\"通常...来\"这类描述\n            /可以.*?进行/,     // 排除\"可以...进行\"这类描述\n            /.*?坐标.*?表示/   // 排除\"坐标...表示\"这类描述\n          ]\n\n          const isDescriptiveText = excludePatterns.some(pattern => pattern.test(optionContent))\n          return !isDescriptiveText\n        }\n      }\n      return false\n    },\n\n    // 判断是否为答案行 - 按照输入规范\n    isAnswerLine(line) {\n      // 规范：显式标注格式（答案：），冒号可以替换为 \":：、\"其中之一\n      return /^答案[.:：、]\\s*/.test(line)\n    },\n\n    // 判断是否为解析行 - 按照输入规范\n    isExplanationLine(line) {\n      // 规范：解析格式（解析：），冒号可以替换为 \":：、\"其中之一\n      return /^解析[.:：、]\\s*/.test(line)\n    },\n\n    // 判断是否为难度行 - 按照输入规范\n    isDifficultyLine(line) {\n      // 规范：难度格式（难度：），冒号可以替换为 \":：、\"其中之一\n      return /^难度[.:：、]\\s*/.test(line)\n    },\n\n    // 获取题目类型显示名称\n    getTypeDisplayName(type) {\n      const typeMap = {\n        'judgment': '判断题',\n        'single': '单选题',\n        'multiple': '多选题',\n        'fill': '填空题',\n        'essay': '简答题'\n      }\n      return typeMap[type] || '判断题'\n    },\n\n    // 处理图片路径，将相对路径转换为完整路径\n    processImagePaths(content) {\n      if (!content || typeof content !== 'string') {\n        return ''\n      }\n\n      try {\n        const processedContent = content.replace(/<img([^>]*?)src=\"([^\"]*?)\"([^>]*?)>/g, (match, before, src, after) => {\n          if (!src) return match\n\n          if (src.startsWith('http://') || src.startsWith('https://') || src.startsWith('data:')) {\n            return match\n          }\n\n          const fullSrc = 'http://localhost:8802' + (src.startsWith('/') ? src : '/' + src)\n          return `<img${before}src=\"${fullSrc}\"${after}>`\n        })\n\n        return processedContent\n      } catch (error) {\n        return content\n      }\n    },\n\n    // 保留富文本格式用于预览显示\n    preserveRichTextFormatting(content) {\n      if (!content || typeof content !== 'string') {\n        return ''\n      }\n\n      try {\n        // 保留常用的富文本格式标签\n        let processedContent = content\n          // 转换相对路径的图片\n          .replace(/<img([^>]*?)src=\"([^\"]*?)\"([^>]*?)>/gi, (match, before, src, after) => {\n            if (!src.startsWith('http') && !src.startsWith('data:')) {\n              const fullSrc = this.processImagePaths(src)\n              return `<img${before}src=\"${fullSrc}\"${after}>`\n            }\n            return match\n          })\n          // 保留段落结构\n          .replace(/<p[^>]*>/gi, '<p>')\n          .replace(/<\\/p>/gi, '</p>')\n          // 保留换行\n          .replace(/<br\\s*\\/?>/gi, '<br>')\n          // 清理多余的空白段落\n          .replace(/<p>\\s*<\\/p>/gi, '')\n          .replace(/(<p>[\\s\\n]*<\\/p>)/gi, '')\n\n        return processedContent.trim()\n      } catch (error) {\n        return content\n      }\n    },\n\n    // 移除HTML标签但保留图片标签\n    stripHtmlTagsKeepImages(content) {\n      if (!content || typeof content !== 'string') {\n        return ''\n      }\n\n      try {\n        const images = []\n        let imageIndex = 0\n        const contentWithPlaceholders = content.replace(/<img[^>]*>/gi, (match) => {\n          images.push(match)\n          return `\\n__IMAGE_PLACEHOLDER_${imageIndex++}__\\n`\n        })\n\n        let textContent = contentWithPlaceholders\n          .replace(/<br\\s*\\/?>/gi, '\\n')\n          .replace(/<\\/p>/gi, '\\n')\n          .replace(/<p[^>]*>/gi, '\\n')\n          .replace(/<[^>]*>/g, '')\n          .replace(/\\n\\s*\\n/g, '\\n')\n          // 处理HTML实体字符\n          .replace(/&nbsp;/g, ' ')      // 非断行空格\n          .replace(/&amp;/g, '&')       // &符号\n          .replace(/&lt;/g, '<')        // 小于号\n          .replace(/&gt;/g, '>')        // 大于号\n          .replace(/&quot;/g, '\"')      // 双引号\n          .replace(/&#39;/g, \"'\")       // 单引号\n          .replace(/&hellip;/g, '...')  // 省略号\n          .replace(/&mdash;/g, '—')     // 长破折号\n          .replace(/&ndash;/g, '–')     // 短破折号\n          .replace(/&ldquo;/g, '\"')     // 左双引号\n          .replace(/&rdquo;/g, '\"')     // 右双引号\n          .replace(/&lsquo;/g, \"'\")     // 左单引号\n          .replace(/&rsquo;/g, \"'\")     // 右单引号\n          .replace(/\\s+/g, ' ')         // 多个空白字符替换为单个空格\n\n        let finalContent = textContent\n        images.forEach((img, index) => {\n          const placeholder = `__IMAGE_PLACEHOLDER_${index}__`\n          if (finalContent.includes(placeholder)) {\n            finalContent = finalContent.replace(placeholder, img)\n          }\n        })\n\n        return finalContent.trim()\n      } catch (error) {\n        return content\n      }\n    },\n\n    // 从行数组解析选项 - 按照输入规范\n    parseOptionsFromLines(lines, startIndex) {\n      const options = []\n\n      if (!Array.isArray(lines) || startIndex < 0 || startIndex >= lines.length) {\n        return { options }\n      }\n\n      try {\n        for (let i = startIndex; i < lines.length; i++) {\n          const line = lines[i]\n\n          if (!line || typeof line !== 'string') {\n            continue\n          }\n\n          // 使用严格的选项行验证逻辑\n          if (this.isOptionLine(line)) {\n            const optionMatch = line.match(/^([A-Za-z])[.:：．、]\\s*(.*)/)\n            if (optionMatch) {\n              const optionKey = optionMatch[1].toUpperCase()\n              const optionContent = optionMatch[2] ? optionMatch[2].trim() : ''\n\n              if (optionKey && optionContent) {\n                options.push({\n                  optionKey: optionKey,\n                  label: optionKey,\n                  optionContent: optionContent,\n                  content: optionContent\n                })\n              }\n            }\n          } else if (this.isAnswerLine(line) || this.isExplanationLine(line) || this.isDifficultyLine(line)) {\n            // 遇到答案、解析或难度行，停止解析选项\n            break\n          } else {\n            // 规范：选项与选项之间，可以换行，也可以在同一行\n            // 如果选项在同一行，选项之间至少需要有一个空格\n            // 但是要避免误将题目内容中的字母+符号识别为选项\n            // 只有当行长度较短且不包含描述性文字时才尝试解析多选项\n            if (line.length < 50 && !/表示|数据|一般|通常|可以/.test(line)) {\n              const multipleOptionsMatch = line.match(/([A-Z][.:：．、]\\s*[^\\s]+(?:\\s+[A-Z][.:：．、]\\s*[^\\s]+)*)/g)\n              if (multipleOptionsMatch) {\n                // 处理同一行多个选项的情况\n                const singleOptions = line.split(/\\s+(?=[A-Za-z][.:：．、])/)\n                for (const singleOption of singleOptions) {\n                  if (!singleOption) continue\n\n                  // 使用严格的选项验证逻辑\n                  if (this.isOptionLine(singleOption)) {\n                    const match = singleOption.match(/^([A-Za-z])[.:：．、]\\s*(.*)/)\n                    if (match) {\n                      const optionKey = match[1].toUpperCase()\n                      const optionContent = match[2] ? match[2].trim() : ''\n\n                      if (optionKey && optionContent) {\n                        options.push({\n                          optionKey: optionKey,\n                          label: optionKey,\n                          optionContent: optionContent,\n                          content: optionContent\n                        })\n                      }\n                    }\n                  }\n                }\n              }\n            }\n          }\n        }\n      } catch (error) {\n        // 忽略错误\n      }\n\n      return { options }\n    },\n\n    // 从行数组解析题目元信息 - 按照输入规范\n    parseQuestionMetaFromLines(lines, question) {\n      for (let i = 0; i < lines.length; i++) {\n        const line = lines[i]\n\n        // 规范：显式标注格式（答案：），冒号可以替换为 \":：、\"其中之一\n        const answerMatch = line.match(/^答案[.:：、]\\s*(.+)/)\n        if (answerMatch) {\n          question.correctAnswer = this.parseAnswerValue(answerMatch[1], question.questionType)\n          continue\n        }\n\n        // 规范：解析格式（解析：），冒号可以替换为 \":：、\"其中之一\n        const explanationMatch = line.match(/^解析[.:：、]\\s*(.+)/)\n        if (explanationMatch) {\n          question.explanation = explanationMatch[1].trim()\n          continue\n        }\n\n        // 规范：难度格式（难度：），只支持简单、中等、困难三个级别\n        const difficultyMatch = line.match(/^难度[.:：、]\\s*(简单|中等|困难|中)/)\n        if (difficultyMatch) {\n          let difficulty = difficultyMatch[1]\n          // 标准化难度值：将\"中\"统一为\"中等\"\n          if (difficulty === '中') {\n            difficulty = '中等'\n          }\n          // 只接受标准的三个难度级别\n          if (['简单', '中等', '困难'].includes(difficulty)) {\n            question.difficulty = difficulty\n          }\n          continue\n        }\n      }\n\n      // 规范：答案支持直接在题干中标注，优先以显式标注的答案为准\n      // 如果没有找到显式答案，尝试从题目内容中提取\n      if (!question.correctAnswer) {\n        question.correctAnswer = this.extractAnswerFromQuestionContent(question.questionContent, question.questionType)\n      }\n    },\n\n    // 从题干中提取答案 - 按照输入规范\n    extractAnswerFromQuestionContent(questionContent, questionType) {\n      if (!questionContent || typeof questionContent !== 'string') {\n        return ''\n      }\n\n      try {\n        // 规范：题干中格式（【A】），括号可以替换为中英文的小括号或者中括号\n        const patterns = [\n          /【([^】]+)】/g,    // 中文方括号\n          /\\[([^\\]]+)\\]/g,   // 英文方括号\n          /（([^）]+)）/g,    // 中文圆括号\n          /\\(([^)]+)\\)/g     // 英文圆括号\n        ]\n\n        for (const pattern of patterns) {\n          const matches = questionContent.match(pattern)\n          if (matches && matches.length > 0) {\n            // 提取最后一个匹配项作为答案（通常答案在题目末尾）\n            const lastMatch = matches[matches.length - 1]\n            const answer = lastMatch.replace(/[【】\\[\\]（）()]/g, '').trim()\n\n            if (answer) {\n              return this.parseAnswerValue(answer, questionType)\n            }\n          }\n        }\n      } catch (error) {\n          // 忽略错误\n        }\n\n      return ''\n    },\n\n    // 解析答案值\n    parseAnswerValue(answerText, questionType) {\n      if (!answerText || typeof answerText !== 'string') {\n        return ''\n      }\n\n      try {\n        const trimmedAnswer = answerText.trim()\n\n        if (!trimmedAnswer) {\n          return ''\n        }\n\n        if (questionType === 'judgment') {\n          // 判断题答案处理 - 保持原始格式，不转换为true/false\n          return trimmedAnswer\n        } else {\n          // 选择题答案处理\n          return trimmedAnswer.toUpperCase()\n        }\n      } catch (error) {\n          return answerText || ''\n        }\n    },\n\n\n\n\n\n    // 获取格式化的题目内容（支持富文本格式）\n    getFormattedQuestionContent(question) {\n      if (!question || !question.questionContent) {\n        return ''\n      }\n\n      let content = question.questionContent\n\n      // 如果有HTML内容且包含富文本标签，优先使用HTML内容\n      if (this.documentHtmlContent && this.documentHtmlContent.includes('<')) {\n        // 从HTML内容中提取对应的题目内容\n        const htmlContent = this.extractQuestionFromHtml(question.questionContent, this.documentHtmlContent)\n        if (htmlContent) {\n          content = htmlContent\n        }\n      }\n\n      // 清理题号：确保题目内容不以数字+符号开头\n      content = this.removeQuestionNumber(content)\n\n      // 清理题型标识：移除题目内容开头的[题型]标识\n      content = this.removeQuestionType(content)\n\n      return this.processImagePaths(content)\n    },\n\n    // 获取题型名称\n    getQuestionTypeName(type) {\n      const typeMap = {\n        'single': '单选题',\n        'multiple': '多选题',\n        'judgment': '判断题'\n      }\n      return typeMap[type] || '未知'\n    },\n\n    // 清理题目内容中的题号\n    removeQuestionNumber(content) {\n      if (!content || typeof content !== 'string') {\n        return content\n      }\n\n      // 处理HTML内容\n      if (content.includes('<')) {\n        // 对于HTML内容，需要清理标签内的题号\n        return content.replace(/<p[^>]*>(\\s*\\d+[.:：．、]\\s*)(.*?)<\\/p>/gi, '<p>$2</p>')\n                     .replace(/^(\\s*\\d+[.:：．、]\\s*)/, '') // 清理开头的题号\n                     .replace(/>\\s*\\d+[.:：．、]\\s*/g, '>') // 清理标签后的题号\n      } else {\n        // 对于纯文本内容，直接清理开头的题号\n        return content.replace(/^\\s*\\d+[.:：．、]\\s*/, '').trim()\n      }\n    },\n\n    // 清理题目内容中的题型标识\n    removeQuestionType(content) {\n      if (!content || typeof content !== 'string') {\n        return content\n      }\n\n      // 处理HTML内容\n      if (content.includes('<')) {\n        // 对于HTML内容，清理标签内的题型标识\n        return content.replace(/<p[^>]*>(\\s*\\[.*?题\\]\\s*)(.*?)<\\/p>/gi, '<p>$2</p>')\n                     .replace(/^(\\s*\\[.*?题\\]\\s*)/, '') // 清理开头的题型标识\n                     .replace(/>\\s*\\[.*?题\\]\\s*/g, '>') // 清理标签后的题型标识\n      } else {\n        // 对于纯文本内容，清理开头的题型标识\n        return content.replace(/^\\s*\\[.*?题\\]\\s*/, '').trim()\n      }\n    },\n\n    // 从HTML内容中提取对应的题目内容\n    extractQuestionFromHtml(plainContent, htmlContent) {\n      if (!plainContent || !htmlContent) {\n        return plainContent\n      }\n\n      try {\n        // 简单的匹配策略：查找包含题目内容的HTML段落\n        const plainText = plainContent.replace(/^\\d+[.:：．、]\\s*/, '').trim()\n\n        // 在HTML内容中查找包含这个文本的段落\n        const paragraphs = htmlContent.match(/<p[^>]*>.*?<\\/p>/gi) || []\n\n        for (const paragraph of paragraphs) {\n          const paragraphText = paragraph.replace(/<[^>]*>/g, '').trim()\n          // 清理段落文本中的题号再进行匹配\n          const cleanParagraphText = paragraphText.replace(/^\\s*\\d+[.:：．、]\\s*/, '').trim()\n          if (cleanParagraphText.includes(plainText.substring(0, 20))) {\n            // 找到匹配的段落，返回HTML格式（但要清理题号）\n            return this.removeQuestionNumber(paragraph)\n          }\n        }\n\n        return plainContent\n      } catch (error) {\n        return plainContent\n      }\n    },\n\n\n    // 搜索\n    handleSearch() {\n      this.queryParams.pageNum = 1\n      this.getQuestionList()\n    },\n    // 重置搜索\n    resetSearch() {\n      this.queryParams.questionType = null\n      this.queryParams.difficulty = null\n      this.queryParams.questionContent = null\n      this.queryParams.pageNum = 1\n      this.getQuestionList()\n    }\n  }\n}\n</script>\n\n<style scoped>\n.page-header {\n  margin-bottom: 20px;\n  padding: 20px;\n  background: #fff;\n  border-radius: 4px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n.header-title {\n  display: flex;\n  align-items: center;\n  margin-bottom: 20px;\n}\n\n.header-content {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  gap: 20px;\n  min-height: 32px;\n}\n\n.search-section {\n  flex: 1;\n}\n\n.search-section .el-form {\n  margin-bottom: 0;\n}\n\n.search-section .el-form-item {\n  margin-bottom: 0;\n}\n\n.stats-section {\n  flex-shrink: 0;\n  padding-top: 0;\n  display: flex;\n  align-items: center;\n  height: 32px;\n}\n\n.stats-container {\n  display: flex;\n  gap: 20px;\n  align-items: center;\n  height: 32px;\n}\n\n.stat-item {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  min-width: 60px;\n  height: 100%;\n}\n\n.stat-label {\n  font-size: 12px;\n  color: #666;\n  line-height: 1;\n  margin-bottom: 2px;\n}\n\n.stat-value {\n  font-size: 16px;\n  font-weight: bold;\n  color: #409EFF;\n  line-height: 1;\n}\n\n.operation-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 16px;\n  padding: 10px 20px;\n  background: #fff;\n  border-radius: 4px;\n  box-shadow: 0 2px 4px rgba(0,0,0,0.1);\n}\n\n\n\n.question-list {\n  min-height: 400px;\n  padding-bottom: 20px; /* 为最后一个题目添加底部边距 */\n}\n\n\n\n.empty-state {\n  text-align: center;\n  padding: 50px 0;\n}\n\n/* 批量导入抽屉样式 */\n.batch-import-drawer .el-drawer__body {\n  padding: 0;\n  height: 100%;\n}\n\n.main {\n  height: 100%;\n  margin: 0;\n}\n\n.col-left, .col-right {\n  height: 100%;\n  padding: 0 5px;\n}\n\n.h100p {\n  height: 100%;\n}\n\n.toolbar {\n  padding: 15px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n}\n\n.toolbar .orange {\n  color: #e6a23c;\n  font-size: 14px;\n}\n\n.toolbar .fr {\n  display: flex;\n  gap: 10px;\n}\n\n.clearfix::after {\n  content: \"\";\n  display: table;\n  clear: both;\n}\n\n.fr {\n  float: right;\n}\n\n.editor-wrapper {\n  flex: 1;\n  height: calc(100vh - 160px); /* 全屏高度减去头部导航和其他元素 */\n  padding: 0;\n}\n\n.rich-editor-container {\n  height: 100%;\n  width: 100%;\n}\n\n.rich-editor-container .cke {\n  height: 100% !important;\n}\n\n.rich-editor-container .cke_contents {\n  height: calc(100% - 80px) !important; /* 减去工具栏和底部状态栏的高度 */\n}\n\n/* 工具栏样式优化 */\n.rich-editor-container .cke_top {\n  background: #f5f5f5 !important;\n  border-bottom: 1px solid #ddd !important;\n  padding: 6px !important;\n}\n\n.rich-editor-container .cke_toolbox {\n  background: transparent !important;\n}\n\n.rich-editor-container .cke_toolbar {\n  background: transparent !important;\n  border: none !important;\n  margin: 2px 4px !important;\n  float: left !important;\n}\n\n.rich-editor-container .cke_button {\n  background: transparent !important;\n  border: 1px solid transparent !important;\n  border-radius: 3px !important;\n  margin: 1px !important;\n}\n\n.rich-editor-container .cke_button:hover {\n  background: #e6e6e6 !important;\n  border-color: #ccc !important;\n}\n\n.rich-editor-container .cke_button_on {\n  background: #d4edfd !important;\n  border-color: #66afe9 !important;\n}\n\n/* 下拉菜单样式 */\n.rich-editor-container .cke_combo {\n  background: transparent !important;\n  border: 1px solid transparent !important;\n  border-radius: 3px !important;\n  margin: 1px !important;\n}\n\n.rich-editor-container .cke_combo:hover {\n  background: #e6e6e6 !important;\n  border-color: #ccc !important;\n}\n\n.rich-editor-container .cke_combo_button {\n  background: transparent !important;\n  border: none !important;\n  padding: 4px 8px !important;\n}\n\n/* 工具栏分组样式 */\n.rich-editor-container .cke_toolgroup {\n  background: transparent !important;\n  border: 1px solid #ddd !important;\n  border-radius: 4px !important;\n  margin: 2px !important;\n  padding: 1px !important;\n}\n\n/* 图像相关样式 */\n.rich-editor-container img {\n  max-width: 100% !important;\n  height: auto !important;\n  border-radius: 4px !important;\n  box-shadow: 0 2px 8px rgba(0,0,0,0.1) !important;\n  margin: 10px 0 !important;\n}\n\n.rich-editor-container .cke_dialog {\n  z-index: 10000 !important;\n}\n\n.rich-editor-container .cke_dialog_background_cover {\n  z-index: 9999 !important;\n}\n\n.fallback-textarea {\n  width: 100%;\n  height: 100%;\n  border: none;\n  border-radius: 0;\n  resize: none;\n  font-family: 'Courier New', monospace;\n  font-size: 14px;\n  line-height: 1.6;\n  padding: 20px;\n  outline: none;\n}\n\n.document-textarea {\n  height: 100% !important;\n}\n\n.document-textarea .el-textarea__inner {\n  height: 100% !important;\n  border: none;\n  border-radius: 0;\n  resize: none;\n  font-family: 'Courier New', monospace;\n  font-size: 14px;\n  line-height: 1.6;\n  padding: 20px;\n}\n\n.checkarea {\n  padding: 15px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n.checkarea .title {\n  font-size: 16px;\n  font-weight: bold;\n  color: #333;\n  margin-right: 15px;\n}\n\n.checkarea .green {\n  color: #67c23a;\n  margin-right: 15px;\n}\n\n.checkarea .red {\n  color: #f56c6c;\n  margin-right: 15px;\n}\n\n.checkarea .mr20 {\n  margin-right: 20px;\n}\n\n.preview-wrapper {\n  flex: 1;\n  height: calc(100% - 120px);\n  overflow: hidden;\n}\n\n.preview-scroll-wrapper {\n  height: 100%;\n  overflow-y: auto;\n  padding: 10px;\n}\n\n.empty-result {\n  display: flex;\n  flex-direction: column;\n  align-items: center;\n  justify-content: center;\n  height: 200px;\n  color: #999;\n}\n\n.empty-result i {\n  font-size: 48px;\n  margin-bottom: 16px;\n}\n\n.empty-result .tip {\n  font-size: 12px;\n  color: #ccc;\n}\n\n.question-item {\n  margin-bottom: 20px;\n  border: 1px solid #e4e7ed;\n  border-radius: 4px;\n  background: #fff;\n}\n\n.question-item .el-card__body {\n  padding: 10px 20px 15px 20px;\n}\n\n.question-top-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 10px;\n}\n\n.question-top-bar .left font {\n  font-weight: bold;\n  color: #333;\n}\n\n.question-content {\n  margin-top: 10px;\n}\n\n/* 题干显示 */\n.question-main-line {\n  margin-bottom: 8px;\n}\n\n.question-main-line .display-latex {\n  margin: 0;\n}\n\n.display-latex {\n  font-size: 14px;\n  line-height: 1.6;\n  color: #333;\n}\n\n/* 富文本格式支持 */\n.rich-text {\n  /* 加粗 */\n  font-weight: normal;\n}\n\n.rich-text strong,\n.rich-text b {\n  font-weight: bold;\n  color: #2c3e50;\n}\n\n.rich-text em,\n.rich-text i {\n  font-style: italic;\n  color: #34495e;\n}\n\n.rich-text u {\n  text-decoration: underline;\n}\n\n.rich-text s,\n.rich-text strike {\n  text-decoration: line-through;\n}\n\n.rich-text p {\n  margin: 8px 0;\n  line-height: 1.6;\n}\n\n.rich-text br {\n  line-height: 1.6;\n}\n\n/* 确保HTML内容正确显示 */\n.rich-text * {\n  max-width: 100%;\n}\n\n.rich-text {\n  word-wrap: break-word;\n}\n\n.question-options {\n  margin: 4px 0 8px 0;\n}\n\n.option-item {\n  padding: 2px 0;\n  padding-left: 10px;\n  font-size: 13px;\n  color: #666;\n  line-height: 1.3;\n}\n\n\n\n/* 文档上传对话框样式 */\n.document-upload-dialog .subtitle {\n  color: #409eff;\n  font-size: 14px;\n  margin: 10px 0;\n}\n\n.document-upload-dialog .el-button--small {\n  margin: 0 5px;\n}\n\n.document-upload-dialog .el-upload-dragger {\n  width: 100%;\n  height: 120px;\n  border: 2px dashed #d9d9d9;\n  border-radius: 6px;\n  cursor: pointer;\n  position: relative;\n  overflow: hidden;\n  transition: border-color 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);\n}\n\n.document-upload-dialog .el-upload-dragger:hover {\n  border-color: #409eff;\n}\n\n.document-upload-dialog .el-upload-dragger .el-icon-upload {\n  font-size: 67px;\n  color: #c0c4cc;\n  margin: 20px 0 16px;\n  line-height: 50px;\n}\n\n.document-upload-dialog .el-upload__text {\n  color: #606266;\n  font-size: 14px;\n  text-align: center;\n}\n\n.document-upload-dialog .el-upload__text em {\n  color: #409eff;\n  font-style: normal;\n}\n\n/* 上传加载动画样式 */\n.upload-loading {\n  padding: 40px 0;\n  color: #409EFF;\n}\n\n.upload-loading .el-icon-loading {\n  font-size: 28px;\n  animation: rotating 2s linear infinite;\n  margin-bottom: 10px;\n}\n\n.upload-loading .el-upload__text {\n  color: #409EFF;\n  font-size: 14px;\n}\n\n@keyframes rotating {\n  0% {\n    transform: rotate(0deg);\n  }\n  100% {\n    transform: rotate(360deg);\n  }\n}\n\n.rules-dialog .rules-content {\n  max-height: 500px;\n  overflow-y: auto;\n}\n\n.rules-content h3 {\n  margin-top: 0;\n  color: #333;\n}\n\n.rule-section {\n  margin-bottom: 25px;\n}\n\n.rule-section h4 {\n  color: #409eff;\n  margin-bottom: 10px;\n}\n\n.rule-section p {\n  margin: 8px 0;\n  line-height: 1.6;\n  color: #666;\n}\n\n.rule-section code {\n  background: #f1f2f3;\n  padding: 2px 6px;\n  border-radius: 3px;\n  font-family: 'Courier New', monospace;\n  color: #e74c3c;\n}\n\n.rule-section ul {\n  margin: 10px 0;\n  padding-left: 20px;\n}\n\n.rule-section li {\n  margin: 5px 0;\n  color: #666;\n}\n\n.example-section {\n  margin-top: 30px;\n}\n\n.example-section h4 {\n  color: #67c23a;\n  margin-bottom: 15px;\n}\n\n.example-code {\n  background: #f8f9fa;\n  border: 1px solid #e4e7ed;\n  border-radius: 6px;\n  padding: 20px;\n  font-family: 'Courier New', monospace;\n  font-size: 13px;\n  line-height: 1.6;\n  color: #333;\n  white-space: pre-wrap;\n  overflow-x: auto;\n}\n\n/* 新的规范对话框样式 */\n.rules-dialog .rules-tabs {\n  margin-top: -20px;\n}\n\n.rules-dialog .example-content {\n  max-height: 60vh;\n  overflow-y: auto;\n  padding: 0 10px;\n}\n\n.rules-dialog .example-item {\n  margin-bottom: 25px;\n  padding: 15px;\n  background: #f8f9fa;\n  border-radius: 6px;\n  border-left: 4px solid #409eff;\n}\n\n.rules-dialog .example-item p {\n  margin: 5px 0;\n  line-height: 1.6;\n  color: #333;\n}\n\n.rules-dialog .example-item p:first-child {\n  font-weight: bold;\n  color: #409eff;\n  margin-bottom: 10px;\n}\n\n.rules-dialog .rule-section p:first-child {\n  color: #409eff;\n  font-weight: bold;\n  margin-bottom: 10px;\n}\n\n/* 预览头部样式 */\n.preview-header {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  padding: 15px 20px;\n  border-bottom: 1px solid #e4e7ed;\n  background: #f8f9fa;\n}\n\n.preview-header h4 {\n  margin: 0;\n  color: #303133;\n  font-size: 16px;\n  font-weight: 600;\n}\n\n.preview-actions {\n  display: flex;\n  align-items: center;\n}\n\n.toggle-all-btn {\n  color: #409eff;\n  font-size: 13px;\n  padding: 5px 10px;\n}\n\n.toggle-all-btn:hover {\n  color: #66b1ff;\n}\n\n.toggle-all-btn i {\n  margin-right: 4px;\n}\n\n/* 导入选项样式 */\n.import-options {\n  display: flex;\n  flex-direction: column;\n  gap: 12px;\n  margin-top: 15px;\n  padding: 15px;\n  background: #f8f9fa;\n  border-radius: 6px;\n  border: 1px solid #e9ecef;\n}\n\n.import-options .el-checkbox {\n  margin-right: 0;\n  margin-bottom: 0;\n}\n\n.import-options .el-checkbox__label {\n  font-size: 14px;\n  color: #333;\n  font-weight: 500;\n}\n\n.import-options .el-tooltip {\n  cursor: help;\n}\n\n.import-progress {\n  margin-top: 20px;\n  padding: 15px;\n  background: #fff;\n  border-radius: 6px;\n  border: 1px solid #e1f5fe;\n}\n\n.import-progress .el-progress {\n  margin-bottom: 0;\n}\n\n.import-progress .el-progress__text {\n  font-size: 14px !important;\n  font-weight: 500;\n  color: #409eff;\n}\n\n/* 题目元信息样式 */\n.question-meta {\n  margin-top: 15px;\n  padding-top: 15px;\n  border-top: 1px solid #f0f0f0;\n}\n\n.question-answer,\n.question-explanation,\n.question-difficulty {\n  margin: 6px 0;\n  padding: 6px 10px;\n  border-radius: 4px;\n  font-size: 13px;\n  line-height: 1.4;\n}\n\n.question-answer {\n  background: #e8f4fd;\n  color: #0066cc;\n  border-left: 3px solid #409eff;\n}\n\n.question-explanation {\n  background: #f0f9ff;\n  color: #666;\n  border-left: 3px solid #67c23a;\n}\n\n.question-difficulty {\n  background: #fef0e6;\n  color: #e6a23c;\n  border-left: 3px solid #e6a23c;\n}\n\n/* 预览滚动区域样式 */\n.preview-scroll-wrapper {\n  padding-bottom: 30px; /* 为最后一个题目添加底部边距 */\n}\n\n/* 题目顶部栏样式 */\n.question-top-bar {\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  margin-bottom: 15px;\n}\n\n.question-title {\n  flex: 1;\n}\n\n.question-toggle {\n  flex-shrink: 0;\n}\n\n.toggle-btn {\n  color: #909399;\n  font-size: 16px;\n  padding: 4px;\n  min-width: auto;\n}\n\n.toggle-btn:hover {\n  color: #409eff;\n}\n\n/* 导入操作区域样式 */\n.import-actions {\n  display: flex;\n  align-items: center;\n  gap: 15px;\n  padding: 15px 20px;\n  background: #f8f9fa;\n  border-bottom: 1px solid #e4e7ed;\n}\n\n/* 未保存更改指示器 */\n.unsaved-indicator {\n  color: #f56c6c;\n  font-size: 18px;\n  margin-left: 8px;\n  animation: blink 1.5s infinite;\n}\n\n@keyframes blink {\n  0%, 50% { opacity: 1; }\n  51%, 100% { opacity: 0.3; }\n}\n</style>\n"]}]}