{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\index.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\index.vue", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgeyBsaXN0UXVlc3Rpb25CYW5rLCBnZXRRdWVzdGlvbkJhbmssIGRlbFF1ZXN0aW9uQmFuaywgYWRkUXVlc3Rpb25CYW5rLCB1cGRhdGVRdWVzdGlvbkJhbmsgfSBmcm9tICJAL2FwaS9iaXovcXVlc3Rpb25CYW5rIg0KaW1wb3J0IHsgbGlzdENhdGVnb3J5IH0gZnJvbSAiQC9hcGkvYml6L2NhdGVnb3J5Ig0KaW1wb3J0IHsgZ2V0UXVlc3Rpb25TdGF0aXN0aWNzLCBnZXRHbG9iYWxRdWVzdGlvblN0YXRpc3RpY3MgfSBmcm9tICJAL2FwaS9iaXovcXVlc3Rpb24iDQppbXBvcnQgKiBhcyBlY2hhcnRzIGZyb20gJ2VjaGFydHMnDQoNCmV4cG9ydCBkZWZhdWx0IHsNCiAgbmFtZTogIlF1ZXN0aW9uQmFuayIsDQogIGRhdGEoKSB7DQogICAgcmV0dXJuIHsNCiAgICAgIC8vIOWfuuehgFVSTA0KICAgICAgYmFzZVVybDogcHJvY2Vzcy5lbnYuVlVFX0FQUF9CQVNFX0FQSSwNCiAgICAgIC8vIOm7mOiupOWbvueJhw0KICAgICAgZGVmYXVsdEltYWdlOiByZXF1aXJlKCdAL2Fzc2V0cy9pbWFnZXMvZGVmYXVsdF9waWMucG5nJyksDQogICAgICAvLyDpga7nvanlsYINCiAgICAgIGxvYWRpbmc6IHRydWUsDQogICAgICAvLyDpgInkuK3mlbDnu4QNCiAgICAgIGlkczogW10sDQogICAgICAvLyDpnZ7ljZXkuKrnpoHnlKgNCiAgICAgIHNpbmdsZTogdHJ1ZSwNCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqA0KICAgICAgbXVsdGlwbGU6IHRydWUsDQogICAgICAvLyDmmL7npLrmkJzntKLmnaHku7YNCiAgICAgIHNob3dTZWFyY2g6IHRydWUsDQogICAgICAvLyDmgLvmnaHmlbANCiAgICAgIHRvdGFsOiAwLA0KICAgICAgLy8g6aKY5bqT6KGo5qC85pWw5o2uDQogICAgICBxdWVzdGlvbkJhbmtMaXN0OiBbXSwNCiAgICAgIC8vIOWIhuexu+mA<PERSON>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"}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0MA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/biz/questionBank", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <!-- 搜索区域和统计图表 -->\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"16\">\r\n        <el-form :model=\"queryParams\" ref=\"queryForm\" size=\"small\" :inline=\"true\" v-show=\"showSearch\" label-width=\"68px\">\r\n          <el-form-item label=\"题库名称\" prop=\"bankName\">\r\n            <el-input\r\n              v-model=\"queryParams.bankName\"\r\n              placeholder=\"请输入题库名称\"\r\n              clearable\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"所属分类\" prop=\"categoryId\">\r\n            <el-cascader\r\n              v-model=\"queryParams.categoryId\"\r\n              :options=\"categoryOptions\"\r\n              :props=\"cascaderProps\"\r\n              placeholder=\"请选择分类\"\r\n              clearable\r\n              style=\"width: 200px\"\r\n              @change=\"handleCategoryChange\">\r\n            </el-cascader>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button type=\"primary\" icon=\"el-icon-search\" size=\"mini\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-col>\r\n      <el-col :span=\"8\">\r\n        <!-- 题型统计饼状图 -->\r\n        <el-card class=\"statistics-card\" v-show=\"showSearch\">\r\n          <div slot=\"header\" class=\"clearfix\">\r\n            <span style=\"font-size: 14px; font-weight: bold;\">题型统计</span>\r\n          </div>\r\n          <div ref=\"pieChart\" style=\"width: 100%; height: 200px;\"></div>\r\n        </el-card>\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <el-row :gutter=\"10\" class=\"mb8\">\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"primary\"\r\n          plain\r\n          icon=\"el-icon-plus\"\r\n          size=\"mini\"\r\n          @click=\"handleAdd\"\r\n          v-hasPermi=\"['biz:questionBank:add']\"\r\n        >新增</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"success\"\r\n          plain\r\n          icon=\"el-icon-edit\"\r\n          size=\"mini\"\r\n          :disabled=\"single\"\r\n          @click=\"handleUpdate\"\r\n          v-hasPermi=\"['biz:questionBank:edit']\"\r\n        >修改</el-button>\r\n      </el-col>\r\n      <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"danger\"\r\n          plain\r\n          icon=\"el-icon-delete\"\r\n          size=\"mini\"\r\n          :disabled=\"multiple\"\r\n          @click=\"handleDelete\"\r\n          v-hasPermi=\"['biz:questionBank:remove']\"\r\n        >删除</el-button>\r\n      </el-col>\r\n      <!-- <el-col :span=\"1.5\">\r\n        <el-button\r\n          type=\"warning\"\r\n          plain\r\n          icon=\"el-icon-download\"\r\n          size=\"mini\"\r\n          @click=\"handleExport\"\r\n          v-hasPermi=\"['biz:questionBank:export']\"\r\n        >导出</el-button>\r\n      </el-col> -->\r\n      <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n    </el-row>\r\n\r\n    <el-table border v-loading=\"loading\" :data=\"questionBankList\" @selection-change=\"handleSelectionChange\">\r\n      <el-table-column type=\"selection\" width=\"55\" align=\"center\" />\r\n      <el-table-column label=\"封面图片\" align=\"center\" prop=\"coverImg\" width=\"100\" min-width=\"100\">\r\n        <template slot-scope=\"scope\">\r\n          <el-image\r\n            :src=\"getImageSrc(scope.row.coverImg)\"\r\n            :preview-src-list=\"[getImageSrc(scope.row.coverImg)]\"\r\n            style=\"width: 50px; height: 50px; object-fit: cover; cursor: pointer;\"\r\n            fit=\"cover\"\r\n            :lazy=\"true\"\r\n          >\r\n            <div slot=\"error\" class=\"image-slot\">\r\n              <i class=\"el-icon-picture-outline\"></i>\r\n            </div>\r\n          </el-image>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"题库名称\" align=\"center\" prop=\"bankName\" min-width=\"150\">\r\n        <template slot-scope=\"scope\">\r\n          <router-link\r\n            :to=\"{ path: '/biz/questionBank/detail', query: { bankId: scope.row.bankId, bankName: scope.row.bankName } }\"\r\n            class=\"link-type\"\r\n            style=\"color: #409EFF; text-decoration: none;\"\r\n          >\r\n            {{ scope.row.bankName }}\r\n          </router-link>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"题库描述\" align=\"center\" prop=\"bankDesc\" min-width=\"200\" show-overflow-tooltip><\r\n        <template slot-scope=\"scope\">\r\n          <span v-if=\"scope.row.bankDesc\">{{ scope.row.bankDesc }}</span>\r\n          <span v-else>--</span>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"所属分类\" align=\"center\" prop=\"categoryId\" min-width=\"120\">\r\n        <template slot-scope=\"scope\">\r\n          {{ getCategoryName(scope.row.categoryId) }}\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"顺序\" align=\"center\" prop=\"orderNum\" width=\"80\" min-width=\"80\" />\r\n      <el-table-column label=\"状态\" align=\"center\" prop=\"status\" width=\"80\" min-width=\"80\">\r\n        <template slot-scope=\"scope\">\r\n          <el-switch\r\n            v-model=\"scope.row.status\"\r\n            :active-value=\"0\"\r\n            :inactive-value=\"1\"\r\n            @change=\"handleStatusChange(scope.row)\"\r\n          ></el-switch>\r\n        </template>\r\n      </el-table-column>\r\n      <el-table-column label=\"操作\" align=\"center\" width=\"150\" min-width=\"150\" fixed=\"right\" class-name=\"small-padding fixed-width\">\r\n        <template slot-scope=\"scope\">\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-edit\"\r\n            @click=\"handleUpdate(scope.row)\"\r\n            v-hasPermi=\"['biz:questionBank:edit']\"\r\n          >修改</el-button>\r\n          <el-button\r\n            size=\"mini\"\r\n            type=\"text\"\r\n            icon=\"el-icon-delete\"\r\n            @click=\"handleDelete(scope.row)\"\r\n            v-hasPermi=\"['biz:questionBank:remove']\"\r\n          >删除</el-button>\r\n        </template>\r\n      </el-table-column>\r\n    </el-table>\r\n    \r\n    <pagination\r\n      v-show=\"total>0\"\r\n      :total=\"total\"\r\n      :page.sync=\"queryParams.pageNum\"\r\n      :limit.sync=\"queryParams.pageSize\"\r\n      @pagination=\"getList\"\r\n    />\r\n\r\n    <!-- 添加或修改题库对话框 -->\r\n    <el-dialog :title=\"title\" :visible.sync=\"open\" width=\"35%\" append-to-body>\r\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" label-width=\"80px\">\r\n        <el-form-item label=\"分类\" prop=\"categoryId\">\r\n          <el-cascader\r\n            v-model=\"form.categoryId\"\r\n            :options=\"categoryOptions\"\r\n            :props=\"cascaderProps\"\r\n            placeholder=\"请选择分类\"\r\n            clearable\r\n            style=\"width: 100%\"\r\n            @change=\"handleFormCategoryChange\">\r\n          </el-cascader>\r\n        </el-form-item>\r\n        <el-form-item label=\"题库名称\" prop=\"bankName\">\r\n          <el-input v-model=\"form.bankName\" placeholder=\"请输入题库名称\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"题库描述\" prop=\"bankDesc\">\r\n          <el-input v-model=\"form.bankDesc\" rows=\"3\" type=\"textarea\" placeholder=\"请输入题库描述\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"顺序\" prop=\"orderNum\">\r\n          <el-input-number v-model=\"form.orderNum\" :min=\"0\" :max=\"9999\" placeholder=\"请输入顺序\" style=\"width: 100%\" />\r\n        </el-form-item>\r\n        <el-form-item label=\"封面图片\" prop=\"coverImg\">\r\n          <image-upload v-model=\"form.coverImg\" :limit=\"1\"/>\r\n        </el-form-item>\r\n      </el-form>\r\n      <div slot=\"footer\" class=\"dialog-footer\">\r\n        <el-button type=\"primary\" @click=\"submitForm\">确 定</el-button>\r\n        <el-button @click=\"cancel\">取 消</el-button>\r\n      </div>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport { listQuestionBank, getQuestionBank, delQuestionBank, addQuestionBank, updateQuestionBank } from \"@/api/biz/questionBank\"\r\nimport { listCategory } from \"@/api/biz/category\"\r\nimport { getQuestionStatistics, getGlobalQuestionStatistics } from \"@/api/biz/question\"\r\nimport * as echarts from 'echarts'\r\n\r\nexport default {\r\n  name: \"QuestionBank\",\r\n  data() {\r\n    return {\r\n      // 基础URL\r\n      baseUrl: process.env.VUE_APP_BASE_API,\r\n      // 默认图片\r\n      defaultImage: require('@/assets/images/default_pic.png'),\r\n      // 遮罩层\r\n      loading: true,\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: true,\r\n      // 总条数\r\n      total: 0,\r\n      // 题库表格数据\r\n      questionBankList: [],\r\n      // 分类选项数据\r\n      categoryOptions: [],\r\n      // 饼状图实例\r\n      pieChart: null,\r\n      // 统计数据\r\n      statisticsData: {\r\n        total: 0,\r\n        singleChoice: 0,\r\n        multipleChoice: 0,\r\n        judgment: 0\r\n      },\r\n      // 级联选择器配置\r\n      cascaderProps: {\r\n        value: 'id',\r\n        label: 'name',\r\n        children: 'children',\r\n        expandTrigger: 'hover',\r\n        emitPath: false // 只返回最后一级的值\r\n      },\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 10,\r\n        bankName: null,\r\n        categoryId: null,\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      // 表单校验\r\n      rules: {\r\n        bankName: [\r\n          { required: true, message: \"题库名称不能为空\", trigger: \"blur\" }\r\n        ],\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n    this.getCategoryList()\r\n    this.getGlobalStatistics()\r\n  },\r\n  mounted() {\r\n    this.$nextTick(() => {\r\n      this.initPieChart()\r\n    })\r\n  },\r\n  beforeDestroy() {\r\n    if (this.pieChart) {\r\n      this.pieChart.dispose()\r\n    }\r\n  },\r\n  methods: {\r\n    /** 查询题库列表 */\r\n    getList() {\r\n      this.loading = true\r\n      listQuestionBank(this.queryParams).then(response => {\r\n        this.questionBankList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n      })\r\n    },\r\n    /** 查询分类列表 */\r\n    getCategoryList() {\r\n      listCategory({ pageSize: 1000 }).then(response => {\r\n        const categories = response.rows || []\r\n        this.categoryOptions = this.buildCategoryTree(categories)\r\n      })\r\n    },\r\n    /** 构建分类树形结构 */\r\n    buildCategoryTree(categories) {\r\n      const map = {}\r\n      const result = []\r\n\r\n      // 先将所有分类放入map中\r\n      categories.forEach(category => {\r\n        map[category.id] = { ...category, children: [] }\r\n      })\r\n\r\n      // 构建完整的树形结构\r\n      const fullTree = []\r\n      categories.forEach(category => {\r\n        if (category.parentId === 0) {\r\n          // 顶级分类\r\n          fullTree.push(map[category.id])\r\n        } else {\r\n          // 子分类\r\n          if (map[category.parentId]) {\r\n            map[category.parentId].children.push(map[category.id])\r\n          }\r\n        }\r\n      })\r\n\r\n      // 清理空的children数组，确保叶子节点没有children属性\r\n      const cleanEmptyChildren = (node) => {\r\n        if (node.children && node.children.length === 0) {\r\n          delete node.children\r\n        } else if (node.children && node.children.length > 0) {\r\n          node.children.forEach(child => cleanEmptyChildren(child))\r\n        }\r\n      }\r\n\r\n      // 只返回第二级及以下的分类（跳过第一级）\r\n      fullTree.forEach(firstLevel => {\r\n        if (firstLevel.children && firstLevel.children.length > 0) {\r\n          // 将第二级分类作为顶级显示\r\n          firstLevel.children.forEach(secondLevel => {\r\n            cleanEmptyChildren(secondLevel)\r\n            result.push(secondLevel)\r\n          })\r\n        }\r\n      })\r\n\r\n      return result\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.reset()\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        bankId: null,\r\n        orderNum: 0,\r\n        bankName: null,\r\n        bankDesc: null,\r\n        categoryId: null,\r\n        coverImg: null,\r\n        status: null,\r\n        createBy: null,\r\n        createTime: null,\r\n        updateTime: null\r\n      }\r\n      this.resetForm(\"form\")\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\")\r\n      this.handleQuery()\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.bankId)\r\n      this.single = selection.length!==1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.reset()\r\n      this.open = true\r\n      this.title = \"添加题库\"\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.reset()\r\n      const bankId = row.bankId || this.ids\r\n      getQuestionBank(bankId).then(response => {\r\n        this.form = response.data\r\n        this.open = true\r\n        this.title = \"修改题库\"\r\n      })\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"form\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.form.bankId != null) {\r\n            updateQuestionBank(this.form).then(() => {\r\n              this.$modal.msgSuccess(\"修改成功\")\r\n              this.open = false\r\n              this.getList()\r\n            })\r\n          } else {\r\n            addQuestionBank(this.form).then(() => {\r\n              this.$modal.msgSuccess(\"新增成功\")\r\n              this.open = false\r\n              this.getList()\r\n            })\r\n          }\r\n        }\r\n      })\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const bankIds = row.bankId || this.ids\r\n      this.$modal.confirm('是否确认删除题库编号为\"' + bankIds + '\"的数据项？').then(function() {\r\n        return delQuestionBank(bankIds)\r\n      }).then(() => {\r\n        this.getList()\r\n        this.$modal.msgSuccess(\"删除成功\")\r\n      }).catch(() => {})\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download('biz/questionBank/export', {\r\n        ...this.queryParams\r\n      }, `questionBank_${new Date().getTime()}.xlsx`)\r\n    },\r\n    /** 根据分类ID获取分类名称 */\r\n    getCategoryName(categoryId) {\r\n      const category = this.findCategoryById(this.categoryOptions, categoryId)\r\n      return category ? category.name : '未分类'\r\n    },\r\n    /** 在树形结构中查找分类 */\r\n    findCategoryById(categories, id) {\r\n      for (let category of categories) {\r\n        if (category.id === id) {\r\n          return category\r\n        }\r\n        if (category.children && category.children.length > 0) {\r\n          const found = this.findCategoryById(category.children, id)\r\n          if (found) {\r\n            return found\r\n          }\r\n        }\r\n      }\r\n      return null\r\n    },\r\n    /** 搜索分类变化处理 */\r\n    handleCategoryChange(value) {\r\n      this.queryParams.categoryId = value\r\n    },\r\n    /** 表单分类变化处理 */\r\n    handleFormCategoryChange(value) {\r\n      this.form.categoryId = value\r\n    },\r\n    /** 状态变化处理 */\r\n    handleStatusChange(row) {\r\n      const text = row.status === 0 ? \"启用\" : \"禁用\"\r\n      this.$modal.confirm('确认要\"' + text + '\"\"' + row.bankName + '\"题库吗？').then(() => {\r\n        return updateQuestionBank(row)\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(text + \"成功\")\r\n        this.getList()\r\n      }).catch(() => {\r\n        // 如果取消或失败，恢复原状态\r\n        row.status = row.status === 0 ? 1 : 0\r\n      })\r\n    },\r\n    /** 获取图片源地址 */\r\n    getImageSrc(coverImg) {\r\n      if (coverImg && coverImg.trim()) {\r\n        return this.baseUrl + coverImg\r\n      }\r\n      return this.defaultImage\r\n    },\r\n    /** 获取全局题型统计 */\r\n    getGlobalStatistics() {\r\n      getGlobalQuestionStatistics().then(response => {\r\n        this.statisticsData = response.data || {\r\n          total: 0,\r\n          singleChoice: 0,\r\n          multipleChoice: 0,\r\n          judgment: 0\r\n        }\r\n        this.$nextTick(() => {\r\n          this.updatePieChart()\r\n        })\r\n      }).catch(() => {\r\n        // 如果API调用失败，使用默认数据\r\n        this.statisticsData = {\r\n          total: 0,\r\n          singleChoice: 0,\r\n          multipleChoice: 0,\r\n          judgment: 0\r\n        }\r\n        this.$nextTick(() => {\r\n          this.updatePieChart()\r\n        })\r\n      })\r\n    },\r\n    /** 初始化饼状图 */\r\n    initPieChart() {\r\n      if (this.$refs.pieChart) {\r\n        this.pieChart = echarts.init(this.$refs.pieChart)\r\n        this.updatePieChart()\r\n      }\r\n    },\r\n    /** 更新饼状图数据 */\r\n    updatePieChart() {\r\n      if (!this.pieChart) return\r\n\r\n      const option = {\r\n        tooltip: {\r\n          trigger: 'item',\r\n          formatter: '{a} <br/>{b}: {c} ({d}%)'\r\n        },\r\n        legend: {\r\n          orient: 'horizontal',\r\n          bottom: '0%',\r\n          left: 'center',\r\n          itemWidth: 10,\r\n          itemHeight: 10,\r\n          textStyle: {\r\n            fontSize: 12\r\n          }\r\n        },\r\n        series: [\r\n          {\r\n            name: '题型统计',\r\n            type: 'pie',\r\n            radius: ['30%', '70%'],\r\n            center: ['50%', '45%'],\r\n            avoidLabelOverlap: false,\r\n            label: {\r\n              show: false,\r\n              position: 'center'\r\n            },\r\n            emphasis: {\r\n              label: {\r\n                show: true,\r\n                fontSize: '14',\r\n                fontWeight: 'bold'\r\n              }\r\n            },\r\n            labelLine: {\r\n              show: false\r\n            },\r\n            data: [\r\n              {\r\n                value: this.statisticsData.singleChoice,\r\n                name: '单选题',\r\n                itemStyle: { color: '#5470c6' }\r\n              },\r\n              {\r\n                value: this.statisticsData.multipleChoice,\r\n                name: '多选题',\r\n                itemStyle: { color: '#91cc75' }\r\n              },\r\n              {\r\n                value: this.statisticsData.judgment,\r\n                name: '判断题',\r\n                itemStyle: { color: '#fac858' }\r\n              }\r\n            ]\r\n          }\r\n        ]\r\n      }\r\n\r\n      this.pieChart.setOption(option)\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n.statistics-card {\r\n  height: 280px;\r\n}\r\n\r\n.statistics-card .el-card__body {\r\n  padding: 10px;\r\n}\r\n\r\n.statistics-card .el-card__header {\r\n  padding: 10px 20px;\r\n  border-bottom: 1px solid #ebeef5;\r\n}\r\n</style>\r\n"]}]}