{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\utils\\request.js", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\utils\\request.js", "mtime": 1754012903761}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\babel.config.js", "mtime": 1750638259743}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_axios", "_interopRequireDefault", "require", "_elementUi", "_store", "_auth", "_errorCode", "_ruoyi", "_cache", "_fileSaver", "downloadLoadingInstance", "is<PERSON><PERSON>gin", "exports", "show", "axios", "defaults", "headers", "service", "create", "baseURL", "process", "env", "VUE_APP_BASE_API", "timeout", "interceptors", "request", "use", "config", "isToken", "isRepeatSubmit", "repeatSubmit", "getToken", "method", "params", "url", "tansParams", "slice", "requestObj", "data", "_typeof2", "default", "JSON", "stringify", "time", "Date", "getTime", "requestSize", "Object", "keys", "length", "limitSize", "session<PERSON>bj", "cache", "session", "getJSON", "undefined", "setJSON", "s_url", "s_data", "s_time", "interval", "message", "Promise", "reject", "Error", "error", "response", "res", "code", "msg", "errorCode", "responseType", "MessageBox", "confirm", "confirmButtonText", "cancelButtonText", "type", "then", "store", "dispatch", "location", "href", "catch", "Message", "Notification", "title", "includes", "substr", "duration", "download", "filename", "Loading", "text", "spinner", "background", "post", "_objectSpread2", "transformRequest", "_ref", "_asyncToGenerator2", "_regenerator2", "m", "_callee", "isBlob", "blob", "resText", "rspObj", "errMsg", "w", "_context", "n", "blobValidate", "Blob", "saveAs", "v", "parse", "close", "a", "_x", "apply", "arguments", "r", "_default"], "sources": ["D:/IDEA_PROJECT/exam/ruoyi-ui/src/utils/request.js"], "sourcesContent": ["import axios from 'axios'\r\nimport { Notification, MessageBox, Message, Loading } from 'element-ui'\r\nimport store from '@/store'\r\nimport { getToken } from '@/utils/auth'\r\nimport errorCode from '@/utils/errorCode'\r\nimport { tansParams, blobValidate } from \"@/utils/ruoyi\"\r\nimport cache from '@/plugins/cache'\r\nimport { saveAs } from 'file-saver'\r\n\r\nlet downloadLoadingInstance\r\n// 是否显示重新登录\r\nexport let isRelogin = { show: false }\r\n\r\naxios.defaults.headers['Content-Type'] = 'application/json;charset=utf-8'\r\n// 创建axios实例\r\nconst service = axios.create({\r\n  // axios中请求配置有baseURL选项，表示请求URL公共部分\r\n  baseURL: process.env.VUE_APP_BASE_API,\r\n  // 超时\r\n  timeout: 30000\r\n})\r\n\r\n// request拦截器\r\nservice.interceptors.request.use(config => {\r\n  // 是否需要设置 token\r\n  const isToken = (config.headers || {}).isToken === false\r\n  // 是否需要防止数据重复提交\r\n  const isRepeatSubmit = (config.headers || {}).repeatSubmit === false\r\n  if (getToken() && !isToken) {\r\n    config.headers['Authorization'] = 'Bearer ' + getToken() // 让每个请求携带自定义token 请根据实际情况自行修改\r\n  }\r\n  // get请求映射params参数\r\n  if (config.method === 'get' && config.params) {\r\n    let url = config.url + '?' + tansParams(config.params)\r\n    url = url.slice(0, -1)\r\n    config.params = {}\r\n    config.url = url\r\n  }\r\n  if (!isRepeatSubmit && (config.method === 'post' || config.method === 'put')) {\r\n    const requestObj = {\r\n      url: config.url,\r\n      data: typeof config.data === 'object' ? JSON.stringify(config.data) : config.data,\r\n      time: new Date().getTime()\r\n    }\r\n    const requestSize = Object.keys(JSON.stringify(requestObj)).length // 请求数据大小\r\n    const limitSize = 5 * 1024 * 1024 // 限制存放数据5M\r\n    if (requestSize >= limitSize) {\r\n\r\n      return config\r\n    }\r\n    const sessionObj = cache.session.getJSON('sessionObj')\r\n    if (sessionObj === undefined || sessionObj === null || sessionObj === '') {\r\n      cache.session.setJSON('sessionObj', requestObj)\r\n    } else {\r\n      const s_url = sessionObj.url                  // 请求地址\r\n      const s_data = sessionObj.data                // 请求数据\r\n      const s_time = sessionObj.time                // 请求时间\r\n      const interval = 1000                         // 间隔时间(ms)，小于此时间视为重复提交\r\n      if (s_data === requestObj.data && requestObj.time - s_time < interval && s_url === requestObj.url) {\r\n        const message = '数据正在处理，请勿重复提交'\r\n\r\n        return Promise.reject(new Error(message))\r\n      } else {\r\n        cache.session.setJSON('sessionObj', requestObj)\r\n      }\r\n    }\r\n  }\r\n  return config\r\n}, error => {\r\n\r\n    Promise.reject(error)\r\n})\r\n\r\n// 响应拦截器\r\nservice.interceptors.response.use(res => {\r\n    // 未设置状态码则默认成功状态\r\n    const code = res.data.code || 200\r\n    // 获取错误信息\r\n    const msg = errorCode[code] || res.data.msg || errorCode['default']\r\n    // 二进制数据则直接返回\r\n    if (res.request.responseType ===  'blob' || res.request.responseType ===  'arraybuffer') {\r\n      return res.data\r\n    }\r\n    if (code === 401) {\r\n      if (!isRelogin.show) {\r\n        isRelogin.show = true\r\n        MessageBox.confirm('登录状态已过期，您可以继续留在该页面，或者重新登录', '系统提示', { confirmButtonText: '重新登录', cancelButtonText: '取消', type: 'warning' }).then(() => {\r\n          isRelogin.show = false\r\n          store.dispatch('LogOut').then(() => {\r\n            location.href = '/index'\r\n          })\r\n      }).catch(() => {\r\n        isRelogin.show = false\r\n      })\r\n    }\r\n      return Promise.reject('无效的会话，或者会话已过期，请重新登录。')\r\n    } else if (code === 500) {\r\n      Message({ message: msg, type: 'error' })\r\n      return Promise.reject(new Error(msg))\r\n    } else if (code === 601) {\r\n      Message({ message: msg, type: 'warning' })\r\n      return Promise.reject('error')\r\n    } else if (code !== 200) {\r\n      Notification.error({ title: msg })\r\n      return Promise.reject('error')\r\n    } else {\r\n      return res.data\r\n    }\r\n  },\r\n  error => {\r\n\r\n    let { message } = error\r\n    if (message == \"Network Error\") {\r\n      message = \"后端接口连接异常\"\r\n    } else if (message.includes(\"timeout\")) {\r\n      message = \"系统接口请求超时\"\r\n    } else if (message.includes(\"Request failed with status code\")) {\r\n      message = \"系统接口\" + message.substr(message.length - 3) + \"异常\"\r\n    }\r\n    Message({ message: message, type: 'error', duration: 5 * 1000 })\r\n    return Promise.reject(error)\r\n  }\r\n)\r\n\r\n// 通用下载方法\r\nexport function download(url, params, filename, config) {\r\n  downloadLoadingInstance = Loading.service({ text: \"正在下载数据，请稍候\", spinner: \"el-icon-loading\", background: \"rgba(0, 0, 0, 0.7)\", })\r\n  return service.post(url, params, {\r\n    transformRequest: [(params) => { return tansParams(params) }],\r\n    headers: { 'Content-Type': 'application/x-www-form-urlencoded' },\r\n    responseType: 'blob',\r\n    ...config\r\n  }).then(async (data) => {\r\n    const isBlob = blobValidate(data)\r\n    if (isBlob) {\r\n      const blob = new Blob([data])\r\n      saveAs(blob, filename)\r\n    } else {\r\n      const resText = await data.text()\r\n      const rspObj = JSON.parse(resText)\r\n      const errMsg = errorCode[rspObj.code] || rspObj.msg || errorCode['default']\r\n      Message.error(errMsg)\r\n    }\r\n    downloadLoadingInstance.close()\r\n  }).catch((r) => {\r\n\r\n    Message.error('下载文件出现错误，请联系管理员！')\r\n    downloadLoadingInstance.close()\r\n  })\r\n}\r\n\r\nexport default service\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,UAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,KAAA,GAAAH,OAAA;AACA,IAAAI,UAAA,GAAAL,sBAAA,CAAAC,OAAA;AACA,IAAAK,MAAA,GAAAL,OAAA;AACA,IAAAM,MAAA,GAAAP,sBAAA,CAAAC,OAAA;AACA,IAAAO,UAAA,GAAAP,OAAA;AAEA,IAAIQ,uBAAuB;AAC3B;AACO,IAAIC,SAAS,GAAAC,OAAA,CAAAD,SAAA,GAAG;EAAEE,IAAI,EAAE;AAAM,CAAC;AAEtCC,cAAK,CAACC,QAAQ,CAACC,OAAO,CAAC,cAAc,CAAC,GAAG,gCAAgC;AACzE;AACA,IAAMC,OAAO,GAAGH,cAAK,CAACI,MAAM,CAAC;EAC3B;EACAC,OAAO,EAAEC,OAAO,CAACC,GAAG,CAACC,gBAAgB;EACrC;EACAC,OAAO,EAAE;AACX,CAAC,CAAC;;AAEF;AACAN,OAAO,CAACO,YAAY,CAACC,OAAO,CAACC,GAAG,CAAC,UAAAC,MAAM,EAAI;EACzC;EACA,IAAMC,OAAO,GAAG,CAACD,MAAM,CAACX,OAAO,IAAI,CAAC,CAAC,EAAEY,OAAO,KAAK,KAAK;EACxD;EACA,IAAMC,cAAc,GAAG,CAACF,MAAM,CAACX,OAAO,IAAI,CAAC,CAAC,EAAEc,YAAY,KAAK,KAAK;EACpE,IAAI,IAAAC,cAAQ,EAAC,CAAC,IAAI,CAACH,OAAO,EAAE;IAC1BD,MAAM,CAACX,OAAO,CAAC,eAAe,CAAC,GAAG,SAAS,GAAG,IAAAe,cAAQ,EAAC,CAAC,EAAC;EAC3D;EACA;EACA,IAAIJ,MAAM,CAACK,MAAM,KAAK,KAAK,IAAIL,MAAM,CAACM,MAAM,EAAE;IAC5C,IAAIC,GAAG,GAAGP,MAAM,CAACO,GAAG,GAAG,GAAG,GAAG,IAAAC,iBAAU,EAACR,MAAM,CAACM,MAAM,CAAC;IACtDC,GAAG,GAAGA,GAAG,CAACE,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACtBT,MAAM,CAACM,MAAM,GAAG,CAAC,CAAC;IAClBN,MAAM,CAACO,GAAG,GAAGA,GAAG;EAClB;EACA,IAAI,CAACL,cAAc,KAAKF,MAAM,CAACK,MAAM,KAAK,MAAM,IAAIL,MAAM,CAACK,MAAM,KAAK,KAAK,CAAC,EAAE;IAC5E,IAAMK,UAAU,GAAG;MACjBH,GAAG,EAAEP,MAAM,CAACO,GAAG;MACfI,IAAI,EAAE,IAAAC,QAAA,CAAAC,OAAA,EAAOb,MAAM,CAACW,IAAI,MAAK,QAAQ,GAAGG,IAAI,CAACC,SAAS,CAACf,MAAM,CAACW,IAAI,CAAC,GAAGX,MAAM,CAACW,IAAI;MACjFK,IAAI,EAAE,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC;IAC3B,CAAC;IACD,IAAMC,WAAW,GAAGC,MAAM,CAACC,IAAI,CAACP,IAAI,CAACC,SAAS,CAACL,UAAU,CAAC,CAAC,CAACY,MAAM,EAAC;IACnE,IAAMC,SAAS,GAAG,CAAC,GAAG,IAAI,GAAG,IAAI,EAAC;IAClC,IAAIJ,WAAW,IAAII,SAAS,EAAE;MAE5B,OAAOvB,MAAM;IACf;IACA,IAAMwB,UAAU,GAAGC,cAAK,CAACC,OAAO,CAACC,OAAO,CAAC,YAAY,CAAC;IACtD,IAAIH,UAAU,KAAKI,SAAS,IAAIJ,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,EAAE,EAAE;MACxEC,cAAK,CAACC,OAAO,CAACG,OAAO,CAAC,YAAY,EAAEnB,UAAU,CAAC;IACjD,CAAC,MAAM;MACL,IAAMoB,KAAK,GAAGN,UAAU,CAACjB,GAAG,EAAkB;MAC9C,IAAMwB,MAAM,GAAGP,UAAU,CAACb,IAAI,EAAgB;MAC9C,IAAMqB,MAAM,GAAGR,UAAU,CAACR,IAAI,EAAgB;MAC9C,IAAMiB,QAAQ,GAAG,IAAI,EAAyB;MAC9C,IAAIF,MAAM,KAAKrB,UAAU,CAACC,IAAI,IAAID,UAAU,CAACM,IAAI,GAAGgB,MAAM,GAAGC,QAAQ,IAAIH,KAAK,KAAKpB,UAAU,CAACH,GAAG,EAAE;QACjG,IAAM2B,OAAO,GAAG,eAAe;QAE/B,OAAOC,OAAO,CAACC,MAAM,CAAC,IAAIC,KAAK,CAACH,OAAO,CAAC,CAAC;MAC3C,CAAC,MAAM;QACLT,cAAK,CAACC,OAAO,CAACG,OAAO,CAAC,YAAY,EAAEnB,UAAU,CAAC;MACjD;IACF;EACF;EACA,OAAOV,MAAM;AACf,CAAC,EAAE,UAAAsC,KAAK,EAAI;EAERH,OAAO,CAACC,MAAM,CAACE,KAAK,CAAC;AACzB,CAAC,CAAC;;AAEF;AACAhD,OAAO,CAACO,YAAY,CAAC0C,QAAQ,CAACxC,GAAG,CAAC,UAAAyC,GAAG,EAAI;EACrC;EACA,IAAMC,IAAI,GAAGD,GAAG,CAAC7B,IAAI,CAAC8B,IAAI,IAAI,GAAG;EACjC;EACA,IAAMC,GAAG,GAAGC,kBAAS,CAACF,IAAI,CAAC,IAAID,GAAG,CAAC7B,IAAI,CAAC+B,GAAG,IAAIC,kBAAS,CAAC,SAAS,CAAC;EACnE;EACA,IAAIH,GAAG,CAAC1C,OAAO,CAAC8C,YAAY,KAAM,MAAM,IAAIJ,GAAG,CAAC1C,OAAO,CAAC8C,YAAY,KAAM,aAAa,EAAE;IACvF,OAAOJ,GAAG,CAAC7B,IAAI;EACjB;EACA,IAAI8B,IAAI,KAAK,GAAG,EAAE;IAChB,IAAI,CAACzD,SAAS,CAACE,IAAI,EAAE;MACnBF,SAAS,CAACE,IAAI,GAAG,IAAI;MACrB2D,qBAAU,CAACC,OAAO,CAAC,2BAA2B,EAAE,MAAM,EAAE;QAAEC,iBAAiB,EAAE,MAAM;QAAEC,gBAAgB,EAAE,IAAI;QAAEC,IAAI,EAAE;MAAU,CAAC,CAAC,CAACC,IAAI,CAAC,YAAM;QACzIlE,SAAS,CAACE,IAAI,GAAG,KAAK;QACtBiE,cAAK,CAACC,QAAQ,CAAC,QAAQ,CAAC,CAACF,IAAI,CAAC,YAAM;UAClCG,QAAQ,CAACC,IAAI,GAAG,QAAQ;QAC1B,CAAC,CAAC;MACN,CAAC,CAAC,CAACC,KAAK,CAAC,YAAM;QACbvE,SAAS,CAACE,IAAI,GAAG,KAAK;MACxB,CAAC,CAAC;IACJ;IACE,OAAOiD,OAAO,CAACC,MAAM,CAAC,sBAAsB,CAAC;EAC/C,CAAC,MAAM,IAAIK,IAAI,KAAK,GAAG,EAAE;IACvB,IAAAe,kBAAO,EAAC;MAAEtB,OAAO,EAAEQ,GAAG;MAAEO,IAAI,EAAE;IAAQ,CAAC,CAAC;IACxC,OAAOd,OAAO,CAACC,MAAM,CAAC,IAAIC,KAAK,CAACK,GAAG,CAAC,CAAC;EACvC,CAAC,MAAM,IAAID,IAAI,KAAK,GAAG,EAAE;IACvB,IAAAe,kBAAO,EAAC;MAAEtB,OAAO,EAAEQ,GAAG;MAAEO,IAAI,EAAE;IAAU,CAAC,CAAC;IAC1C,OAAOd,OAAO,CAACC,MAAM,CAAC,OAAO,CAAC;EAChC,CAAC,MAAM,IAAIK,IAAI,KAAK,GAAG,EAAE;IACvBgB,uBAAY,CAACnB,KAAK,CAAC;MAAEoB,KAAK,EAAEhB;IAAI,CAAC,CAAC;IAClC,OAAOP,OAAO,CAACC,MAAM,CAAC,OAAO,CAAC;EAChC,CAAC,MAAM;IACL,OAAOI,GAAG,CAAC7B,IAAI;EACjB;AACF,CAAC,EACD,UAAA2B,KAAK,EAAI;EAEP,IAAMJ,OAAO,GAAKI,KAAK,CAAjBJ,OAAO;EACb,IAAIA,OAAO,IAAI,eAAe,EAAE;IAC9BA,OAAO,GAAG,UAAU;EACtB,CAAC,MAAM,IAAIA,OAAO,CAACyB,QAAQ,CAAC,SAAS,CAAC,EAAE;IACtCzB,OAAO,GAAG,UAAU;EACtB,CAAC,MAAM,IAAIA,OAAO,CAACyB,QAAQ,CAAC,iCAAiC,CAAC,EAAE;IAC9DzB,OAAO,GAAG,MAAM,GAAGA,OAAO,CAAC0B,MAAM,CAAC1B,OAAO,CAACZ,MAAM,GAAG,CAAC,CAAC,GAAG,IAAI;EAC9D;EACA,IAAAkC,kBAAO,EAAC;IAAEtB,OAAO,EAAEA,OAAO;IAAEe,IAAI,EAAE,OAAO;IAAEY,QAAQ,EAAE,CAAC,GAAG;EAAK,CAAC,CAAC;EAChE,OAAO1B,OAAO,CAACC,MAAM,CAACE,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACO,SAASwB,QAAQA,CAACvD,GAAG,EAAED,MAAM,EAAEyD,QAAQ,EAAE/D,MAAM,EAAE;EACtDjB,uBAAuB,GAAGiF,kBAAO,CAAC1E,OAAO,CAAC;IAAE2E,IAAI,EAAE,YAAY;IAAEC,OAAO,EAAE,iBAAiB;IAAEC,UAAU,EAAE;EAAsB,CAAC,CAAC;EAChI,OAAO7E,OAAO,CAAC8E,IAAI,CAAC7D,GAAG,EAAED,MAAM,MAAA+D,cAAA,CAAAxD,OAAA;IAC7ByD,gBAAgB,EAAE,CAAC,UAAChE,MAAM,EAAK;MAAE,OAAO,IAAAE,iBAAU,EAACF,MAAM,CAAC;IAAC,CAAC,CAAC;IAC7DjB,OAAO,EAAE;MAAE,cAAc,EAAE;IAAoC,CAAC;IAChEuD,YAAY,EAAE;EAAM,GACjB5C,MAAM,CACV,CAAC,CAACkD,IAAI;IAAA,IAAAqB,IAAA,OAAAC,kBAAA,CAAA3D,OAAA,mBAAA4D,aAAA,CAAA5D,OAAA,IAAA6D,CAAA,CAAC,SAAAC,QAAOhE,IAAI;MAAA,IAAAiE,MAAA,EAAAC,IAAA,EAAAC,OAAA,EAAAC,MAAA,EAAAC,MAAA;MAAA,WAAAP,aAAA,CAAA5D,OAAA,IAAAoE,CAAA,WAAAC,QAAA;QAAA,kBAAAA,QAAA,CAAAC,CAAA;UAAA;YACXP,MAAM,GAAG,IAAAQ,mBAAY,EAACzE,IAAI,CAAC;YAAA,KAC7BiE,MAAM;cAAAM,QAAA,CAAAC,CAAA;cAAA;YAAA;YACFN,IAAI,GAAG,IAAIQ,IAAI,CAAC,CAAC1E,IAAI,CAAC,CAAC;YAC7B,IAAA2E,iBAAM,EAACT,IAAI,EAAEd,QAAQ,CAAC;YAAAmB,QAAA,CAAAC,CAAA;YAAA;UAAA;YAAAD,QAAA,CAAAC,CAAA;YAAA,OAEAxE,IAAI,CAACsD,IAAI,CAAC,CAAC;UAAA;YAA3Ba,OAAO,GAAAI,QAAA,CAAAK,CAAA;YACPR,MAAM,GAAGjE,IAAI,CAAC0E,KAAK,CAACV,OAAO,CAAC;YAC5BE,MAAM,GAAGrC,kBAAS,CAACoC,MAAM,CAACtC,IAAI,CAAC,IAAIsC,MAAM,CAACrC,GAAG,IAAIC,kBAAS,CAAC,SAAS,CAAC;YAC3Ea,kBAAO,CAAClB,KAAK,CAAC0C,MAAM,CAAC;UAAA;YAEvBjG,uBAAuB,CAAC0G,KAAK,CAAC,CAAC;UAAA;YAAA,OAAAP,QAAA,CAAAQ,CAAA;QAAA;MAAA,GAAAf,OAAA;IAAA,CAChC;IAAA,iBAAAgB,EAAA;MAAA,OAAApB,IAAA,CAAAqB,KAAA,OAAAC,SAAA;IAAA;EAAA,IAAC,CAACtC,KAAK,CAAC,UAACuC,CAAC,EAAK;IAEdtC,kBAAO,CAAClB,KAAK,CAAC,kBAAkB,CAAC;IACjCvD,uBAAuB,CAAC0G,KAAK,CAAC,CAAC;EACjC,CAAC,CAAC;AACJ;AAAC,IAAAM,QAAA,GAAA9G,OAAA,CAAA4B,OAAA,GAEcvB,OAAO", "ignoreList": []}]}