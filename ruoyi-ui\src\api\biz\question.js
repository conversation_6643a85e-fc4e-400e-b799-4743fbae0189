import request from '@/utils/request'

// 查询题目列表
export function listQuestion(query) {
  return request({
    url: '/biz/question/list',
    method: 'get',
    params: query
  })
}

// 查询题目详细
export function getQuestion(questionId) {
  return request({
    url: '/biz/question/' + questionId,
    method: 'get'
  })
}

// 新增题目
export function addQuestion(data) {
  return request({
    url: '/biz/question',
    method: 'post',
    data: data
  })
}

// 修改题目
export function updateQuestion(data) {
  return request({
    url: '/biz/question',
    method: 'put',
    data: data
  })
}

// 删除题目
export function delQuestion(questionId) {
  return request({
    url: '/biz/question/' + questionId,
    method: 'delete'
  })
}

// 获取题库统计信息
export function getQuestionStatistics(bankId) {
  return request({
    url: '/biz/question/statistics/' + bankId,
    method: 'get'
  })
}

// 获取全局题型统计信息
export function getGlobalQuestionStatistics() {
  return request({
    url: '/biz/question/globalStatistics',
    method: 'get'
  })
}

// 批量导入题目
export function batchImportQuestions(data) {
  return request({
    url: '/biz/question/batchImport',
    method: 'post',
    data: data
  })
}

// 解析导入文件
export function parseImportFile(data) {
  return request({
    url: '/biz/question/parseFile',
    method: 'post',
    data: data
  })
}

// 下载导入模板
export function downloadTemplate(questionType) {
  return request({
    url: '/biz/question/downloadTemplate',
    method: 'get',
    params: { questionType },
    responseType: 'blob'
  })
}

// 复制题目
export function copyQuestion(questionId) {
  return request({
    url: '/biz/question/copy/' + questionId,
    method: 'post'
  })
}

// 导出题目为Word文档
export function exportQuestionsToWord(params) {
  return request({
    url: '/biz/question/exportWord',
    method: 'get',
    params: params,
    responseType: 'blob'
  })
}
