-- 题目表测试数据
-- 用于验证批量导入功能

-- 清空现有测试数据
DELETE FROM tbl_question WHERE bank_id = 1;

-- 插入测试题目数据
INSERT INTO tbl_question (bank_id, question_type, difficulty, question_content, analysis, options, create_by, create_time, update_time) VALUES
-- 单选题
(1, 1, 1, '《诗经》是我国最早的诗歌总集，共收录了多少首诗？', '《诗经》共收录了305首诗，分为风、雅、颂三个部分。', '[{"optionKey":"A","optionContent":"300首","isCorrect":false},{"optionKey":"B","optionContent":"305首","isCorrect":true},{"optionKey":"C","optionContent":"310首","isCorrect":false},{"optionKey":"D","optionContent":"315首","isCorrect":false}]', 'admin', NOW(), NOW()),

(1, 1, 2, '下列哪位诗人被称为"诗仙"？', '李白因其诗歌风格豪放飘逸，想象丰富，被后人称为"诗仙"。', '[{"optionKey":"A","optionContent":"杜甫","isCorrect":false},{"optionKey":"B","optionContent":"李白","isCorrect":true},{"optionKey":"C","optionContent":"白居易","isCorrect":false},{"optionKey":"D","optionContent":"王维","isCorrect":false}]', 'admin', NOW(), NOW()),

(1, 1, 3, '《红楼梦》的作者是谁？', '《红楼梦》是清代作家曹雪芹创作的章回体长篇小说。', '[{"optionKey":"A","optionContent":"吴承恩","isCorrect":false},{"optionKey":"B","optionContent":"施耐庵","isCorrect":false},{"optionKey":"C","optionContent":"曹雪芹","isCorrect":true},{"optionKey":"D","optionContent":"罗贯中","isCorrect":false}]', 'admin', NOW(), NOW()),

-- 多选题
(1, 2, 2, '下列属于唐代诗人的有哪些？', '这些都是唐代著名的诗人，各有其独特的诗歌风格。', '[{"optionKey":"A","optionContent":"李白","isCorrect":true},{"optionKey":"B","optionContent":"杜甫","isCorrect":true},{"optionKey":"C","optionContent":"王维","isCorrect":true},{"optionKey":"D","optionContent":"苏轼","isCorrect":false}]', 'admin', NOW(), NOW()),

(1, 2, 2, '中国古代四大名著包括哪些？', '中国古代四大名著是指《红楼梦》、《西游记》、《水浒传》、《三国演义》。', '[{"optionKey":"A","optionContent":"《红楼梦》","isCorrect":true},{"optionKey":"B","optionContent":"《西游记》","isCorrect":true},{"optionKey":"C","optionContent":"《水浒传》","isCorrect":true},{"optionKey":"D","optionContent":"《三国演义》","isCorrect":true}]', 'admin', NOW(), NOW()),

-- 判断题
(1, 3, 1, '《论语》是孔子亲自编写的著作。', '《论语》是孔子弟子及其再传弟子记录孔子及其弟子言行而编成的语录集，并非孔子亲自编写。', '[{"optionKey":"A","optionContent":"正确","isCorrect":false},{"optionKey":"B","optionContent":"错误","isCorrect":true}]', 'admin', NOW(), NOW()),

(1, 3, 1, '李清照是宋代著名的女词人。', '李清照是宋代著名的女词人，号易安居士，被誉为"千古第一才女"。', '[{"optionKey":"A","optionContent":"正确","isCorrect":true},{"optionKey":"B","optionContent":"错误","isCorrect":false}]', 'admin', NOW(), NOW()),

(1, 3, 2, '《史记》是我国第一部纪传体通史。', '《史记》是西汉史学家司马迁撰写的纪传体史书，是中国历史上第一部纪传体通史。', '[{"optionKey":"A","optionContent":"正确","isCorrect":true},{"optionKey":"B","optionContent":"错误","isCorrect":false}]', 'admin', NOW(), NOW());

-- 查询验证数据
SELECT 
    question_id,
    bank_id,
    question_type,
    CASE question_type 
        WHEN 1 THEN '单选题'
        WHEN 2 THEN '多选题' 
        WHEN 3 THEN '判断题'
        ELSE '未知'
    END as type_name,
    difficulty,
    CASE difficulty
        WHEN 1 THEN '简单'
        WHEN 2 THEN '中等'
        WHEN 3 THEN '困难'
        ELSE '未知'
    END as difficulty_name,
    LEFT(question_content, 50) as content_preview,
    create_time
FROM tbl_question 
WHERE bank_id = 1 
ORDER BY question_type, create_time;

-- 统计各题型数量
SELECT 
    question_type,
    CASE question_type 
        WHEN 1 THEN '单选题'
        WHEN 2 THEN '多选题' 
        WHEN 3 THEN '判断题'
        ELSE '未知'
    END as type_name,
    COUNT(*) as count
FROM tbl_question 
WHERE bank_id = 1 
GROUP BY question_type
ORDER BY question_type;
