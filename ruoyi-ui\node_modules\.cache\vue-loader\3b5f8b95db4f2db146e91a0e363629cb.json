{"remainingRequest": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\index.vue?vue&type=template&id=ca4f1f4a&scoped=true", "dependencies": [{"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\src\\views\\biz\\questionBank\\index.vue", "mtime": *************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": ************}, {"path": "D:\\IDEA_PROJECT\\exam\\ruoyi-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": ************}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}]}