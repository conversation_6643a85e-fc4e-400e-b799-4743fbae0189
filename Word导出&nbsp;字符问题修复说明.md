# Word导出&nbsp;字符问题修复说明

## 问题描述
在导出Word文档时，题干内容中出现了大量的"&nbsp;"字符，这是因为HTML实体字符没有被正确处理导致的。

## 问题原因
1. **后端问题**：`QuestionServiceImpl.java`中的`cleanHtmlContent`方法只是简单地移除HTML标签，但没有处理HTML实体字符如`&nbsp;`、`&amp;`等。
2. **前端问题**：前端的HTML内容处理方法也没有正确处理HTML实体字符。

## 修复方案

### 1. 后端修复
修改`ruoyi-biz/src/main/java/com/ruoyi/biz/service/impl/QuestionServiceImpl.java`中的`cleanHtmlContent`方法：

**修改前**：
```java
private String cleanHtmlContent(String content) {
    if (content == null) {
        return "";
    }
    // 简单的HTML标签清理
    return content.replaceAll("<[^>]*>", "").trim();
}
```

**修改后**：
```java
private String cleanHtmlContent(String content) {
    if (content == null) {
        return "";
    }
    
    // 1. 移除HTML标签
    String cleaned = content.replaceAll("<[^>]*>", "");
    
    // 2. 处理HTML实体字符
    cleaned = cleaned.replace("&nbsp;", " ")      // 非断行空格
                    .replace("&amp;", "&")        // &符号
                    .replace("&lt;", "<")         // 小于号
                    .replace("&gt;", ">")         // 大于号
                    .replace("&quot;", "\"")      // 双引号
                    .replace("&#39;", "'")        // 单引号
                    .replace("&hellip;", "...")   // 省略号
                    .replace("&mdash;", "-")      // 长破折号
                    .replace("&ndash;", "-")      // 短破折号
                    .replace("&ldquo;", "\"")     // 左双引号
                    .replace("&rdquo;", "\"")     // 右双引号
                    .replace("&lsquo;", "'")      // 左单引号
                    .replace("&rsquo;", "'");     // 右单引号
    
    // 3. 清理多余的空白字符
    cleaned = cleaned.replaceAll("\\s+", " ");    // 多个空白字符替换为单个空格
    
    return cleaned.trim();
}
```

### 2. 前端修复
修改`ruoyi-ui/src/views/biz/questionBank/detail.vue`中的`stripHtmlTagsKeepImages`方法，在HTML标签清理后添加HTML实体字符处理：

```javascript
let textContent = contentWithPlaceholders
  .replace(/<br\s*\/?>/gi, '\n')
  .replace(/<\/p>/gi, '\n')
  .replace(/<p[^>]*>/gi, '\n')
  .replace(/<[^>]*>/g, '')
  .replace(/\n\s*\n/g, '\n')
  // 处理HTML实体字符
  .replace(/&nbsp;/g, ' ')      // 非断行空格
  .replace(/&amp;/g, '&')       // &符号
  .replace(/&lt;/g, '<')        // 小于号
  .replace(/&gt;/g, '>')        // 大于号
  .replace(/&quot;/g, '"')      // 双引号
  .replace(/&#39;/g, "'")       // 单引号
  .replace(/&hellip;/g, '...')  // 省略号
  .replace(/&mdash;/g, '—')     // 长破折号
  .replace(/&ndash;/g, '–')     // 短破折号
  .replace(/&ldquo;/g, '"')     // 左双引号
  .replace(/&rdquo;/g, '"')     // 右双引号
  .replace(/&lsquo;/g, "'")     // 左单引号
  .replace(/&rsquo;/g, "'")     // 右单引号
  .replace(/\s+/g, ' ')         // 多个空白字符替换为单个空格
```

## 修复效果

### 修复前
导出的Word文档中题干内容可能显示为：
```
1、[单选题]计算机的&nbsp;主要&nbsp;组成&nbsp;部分&nbsp;包括（&nbsp;&nbsp;）。
```

### 修复后
导出的Word文档中题干内容将正确显示为：
```
1、[单选题]计算机的 主要 组成 部分 包括（  ）。
```

## 处理的HTML实体字符列表

| HTML实体 | 含义 | 转换后 |
|---------|------|--------|
| `&nbsp;` | 非断行空格 | 空格 |
| `&amp;` | &符号 | & |
| `&lt;` | 小于号 | < |
| `&gt;` | 大于号 | > |
| `&quot;` | 双引号 | " |
| `&#39;` | 单引号 | ' |
| `&hellip;` | 省略号 | ... |
| `&mdash;` | 长破折号 | - |
| `&ndash;` | 短破折号 | - |
| `&ldquo;` | 左双引号 | " |
| `&rdquo;` | 右双引号 | " |
| `&lsquo;` | 左单引号 | ' |
| `&rsquo;` | 右单引号 | ' |

## 测试建议

1. **创建测试题目**：在题库中添加包含HTML实体字符的题目
2. **导出测试**：导出包含这些题目的Word文档
3. **验证结果**：检查导出的Word文档中是否还有`&nbsp;`等字符
4. **前端测试**：在批量导入功能中测试HTML内容的解析是否正确

## 注意事项

1. 这个修复主要针对导出Word功能，不会影响题目在系统中的存储和显示
2. 修复后的内容会更加干净，去除了多余的空白字符
3. 如果发现还有其他HTML实体字符需要处理，可以按照相同的模式添加到处理列表中
